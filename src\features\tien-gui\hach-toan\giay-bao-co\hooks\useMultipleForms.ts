import { useCallback, useEffect, useMemo, useState } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { transformDataFromHoaDon } from '../utils/transform-data';
import { useAuth } from '@/contexts/auth-context';
import { TaiKhoan } from '@/types/schemas';
import { QUERY_KEYS } from '@/constants';
import { useCRUD } from '@/hooks';
import api from '@/lib/api';

export function useInvoiceParamsPresence() {
  const searchParams = useSearchParams();
  return useMemo(() => Boolean(searchParams.get('hdbh') || searchParams.get('hdbdv')), [searchParams]);
}

interface UseMultipleFormsArgs {
  addItem: (data: any) => Promise<any>;
}

export function useMultipleForms({ addItem }: UseMultipleFormsArgs) {
  const searchParams = useSearchParams();
  const router = useRouter();
  const { entity } = useAuth();

  const hasInvoiceParams = useInvoiceParamsPresence();

  const [invoiceData, setInvoiceData] = useState<any[]>([]);
  const [showMultipleForms, setShowMultipleForms] = useState<boolean>(hasInvoiceParams);
  const [currentFormIndex, setCurrentFormIndex] = useState<number>(0);

  // Fetch tai khoan for default tk_data in initial form data
  const taiKhoanSearchData = useMemo(() => ({ prefix: '1111' }), []);
  const { data: taiKhoan, isLoading: isLoadingTaiKhoan } = useCRUD<TaiKhoan, any>({
    endpoint: QUERY_KEYS.TAI_KHOAN,
    searchData: taiKhoanSearchData
  });

  // Function to fetch invoice data by UUID
  const fetchInvoiceByUuid = useCallback(
    async (uuid: string, isHoaDonBanHang: boolean = false) => {
      if (!entity?.slug) return null;

      try {
        const endpoint = isHoaDonBanHang ? QUERY_KEYS.HOA_DON_BAN_HANG : QUERY_KEYS.HOA_DON_BAN_DICH_VU;
        const response = await api.get(`/entities/${entity.slug}/erp/${endpoint}/${uuid}/`);
        return response.data;
      } catch (error) {
        return null;
      }
    },
    [entity?.slug]
  );

  // Handle URL params for creating phieu thu from invoices
  useEffect(() => {
    const hdbhParam = searchParams.get('hdbh');
    const hdbdvParam = searchParams.get('hdbdv');

    if (hdbhParam || hdbdvParam) {
      setShowMultipleForms(true);

      const invoiceUuids = (hdbhParam || hdbdvParam)?.split(',').filter(Boolean) || [];
      const isHoaDonBanHang = !!hdbhParam;

      if (invoiceUuids.length > 0) {
        Promise.all(invoiceUuids.map(uuid => fetchInvoiceByUuid(uuid, isHoaDonBanHang)))
          .then(invoices => {
            const validInvoices = invoices.filter(Boolean);
            setInvoiceData(validInvoices);
            if (validInvoices.length > 0) {
              setCurrentFormIndex(0);
            }
          })
          .catch(error => {
            console.error('Error fetching invoices:', error);
          });
      }
    }
  }, [searchParams, entity?.slug, fetchInvoiceByUuid]);

  // Multiple forms submit handler
  const handleMultipleFormSubmit = useCallback(
    async (data: any) => {
      try {
        await addItem(data);
        if (currentFormIndex < invoiceData.length - 1) {
          setShowMultipleForms(true);
          setCurrentFormIndex(currentFormIndex + 1);
        } else {
          setShowMultipleForms(false);
          setInvoiceData([]);
          setCurrentFormIndex(0);
          router.back();
        }
      } catch (error) {
        return;
      }
    },
    [addItem, currentFormIndex, invoiceData.length, router]
  );

  // Close handler for multiple forms
  const handleCloseMultipleForms = useCallback(() => {
    setShowMultipleForms(false);
    setInvoiceData([]);
    setCurrentFormIndex(0);
  }, []);

  // Build initial form data from current invoice + default tk_data
  let initialData = useMemo(
    () => transformDataFromHoaDon(invoiceData[currentFormIndex]),
    [invoiceData, currentFormIndex]
  );

  if (!isLoadingTaiKhoan && taiKhoan && taiKhoan.length > 0) {
    initialData = {
      ...initialData,
      tk_data: taiKhoan[0] as any
    } as any;
  }

  return {
    hasInvoiceParams,
    showMultipleForms,
    invoiceData,
    currentFormIndex,
    initialData,
    handleMultipleFormSubmit,
    handleCloseMultipleForms
  } as const;
}
