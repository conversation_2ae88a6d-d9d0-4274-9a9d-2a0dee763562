import { useFormContext } from 'react-hook-form';
import React from 'react';
import {
  customerSearchColumns,
  documentTypeSearchColumns,
  foreignCurrencySearchColumns,
  permissionSearchColumns
} from '../cols-definition';
import AritoFormDateRangeDropdown from '@/components/custom/arito/form/form-date-range-dropdown';
import { FormField, SearchField, UnitDropdown } from '@/components/custom/arito/form';
import { ChungTu, KhachHang, NgoaiTe, QuyenChungTu } from '@/types/schemas';
import { QUERY_KEYS, quyenChungTuSearchColumns } from '@/constants';
import { Label } from '@/components/ui/label';
import { useFormFieldState } from '../hooks';

export const BasicInfo: React.FC = () => {
  const { state, actions } = useFormFieldState();
  const { setValue } = useFormContext();

  return (
    <div className='space-y-4 p-4'>
      {/* Processing type */}
      <div className='flex items-center'>
        <Label className='w-40 min-w-40'>Xử lý</Label>
        <div className='w-1/2'>
          <FormField
            name='xu_ly'
            type='select'
            options={[
              { value: '1', label: 'Tạo' },
              { value: '2', label: 'Xóa' }
            ]}
          />
        </div>
      </div>

      {/* Document date range */}
      <div className='flex w-full items-center'>
        <Label className='w-40 min-w-40'>Ngày từ/đến:</Label>
        <div className='w-1/2'>
          <AritoFormDateRangeDropdown fromDateName='ngay_ct1' toDateName='ngay_ct2' />
        </div>
      </div>

      {/* Document number range */}
      <div className='flex items-center'>
        <Label className='w-40 min-w-40'>Số c/từ(từ/đến)</Label>
        <div className='grid w-1/2 grid-cols-2 gap-2'>
          <FormField name='so_ct1' type='text' />
          <FormField name='so_ct2' type='text' />
        </div>
      </div>

      {/* Document type */}
      <div className='flex items-center'>
        <Label className='w-40 min-w-40'>Chứng từ</Label>
        <SearchField<ChungTu>
          name='ma_ct'
          searchEndpoint={`/${QUERY_KEYS.CHUNG_TU}/by-document-types?ma_ct=HD1,HD2`}
          searchColumns={documentTypeSearchColumns}
          columnDisplay='ma_ct'
          displayRelatedField='ten_ct'
          value={state.chungTu?.ma_ct}
          onRowSelection={row => {
            actions.setChungTu(row);
            setValue('ma_ct', row.uuid);
            setValue('ct', row.ma_ct);
          }}
        />
      </div>

      {/* Customer code */}
      <div className='flex items-center'>
        <Label className='w-40 min-w-40'>Mã khách hàng</Label>
        <SearchField<KhachHang>
          name='ma_kh'
          searchEndpoint={`/${QUERY_KEYS.KHACH_HANG}`}
          searchColumns={customerSearchColumns}
          columnDisplay='customer_code'
          displayRelatedField='customer_name'
          value={state.khachHang?.customer_code}
          onRowSelection={row => {
            actions.setKhachHang(row);
            setValue('ma_kh', row.uuid);
          }}
        />
      </div>

      {/* Foreign currency */}
      <div className='flex items-center'>
        <Label className='w-40 min-w-40'>Ngoại tệ</Label>
        <SearchField<NgoaiTe>
          name='ma_nt'
          searchEndpoint={`/${QUERY_KEYS.NGOAI_TE}`}
          searchColumns={foreignCurrencySearchColumns}
          columnDisplay='ma_nt'
          displayRelatedField='ten_nt'
          value={state.ngoaiTe?.ma_nt}
          onRowSelection={row => {
            actions.setNgoaiTe(row);
            setValue('ma_nt', row.uuid);
          }}
        />
      </div>

      {/* Permission */}
      <div className='flex items-center'>
        <Label className='w-40 min-w-40'>Quyển ct</Label>
        <SearchField<QuyenChungTu>
          name='ma_nk'
          searchEndpoint={`/${QUERY_KEYS.QUYEN_CHUNG_TU}/by-document-types?ma_ct=HD1,HD2`}
          searchColumns={quyenChungTuSearchColumns}
          columnDisplay='ten_nk'
          displayRelatedField='ma_nk'
          value={state.quyenChungTu?.ma_nk}
          onRowSelection={row => {
            actions.setQuyenChungTu(row);
            setValue('ma_nk', row.uuid);
          }}
        />
      </div>

      {/* Department */}
      <UnitDropdown formMode='add' className='w-full' labelClassName='w-40 min-w-40' />

      {/* Filter by user */}
      <div className='flex items-center'>
        <Label className='w-40 min-w-40'>Lọc theo người sd</Label>
        <div className='w-1/2'>
          <FormField
            name='user_id0'
            type='select'
            options={[
              { value: '0', label: 'Tất cả' },
              { value: '1', label: 'Lọc theo người tạo' }
            ]}
          />
        </div>
      </div>
    </div>
  );
};
