import { useState, useCallback } from 'react';
import { QUERY_KEYS } from '@/constants/query-keys';
import { useAuth } from '@/contexts/auth-context';
import api from '@/lib/api';

interface ApprovalHistoryRecord {
  id: string;
  history_date: string;
  history_type: string;
  history_user: string;
  history_change_reason: string;
  action: string;
  cnote: string | null;
  xprocess: string | null;
  xstatus: string | null;
}

interface ApprovalHistoryResponse {
  count: number;
  next: string | null;
  previous: string | null;
  results: ApprovalHistoryRecord[];
}

interface UseApprovalHistoryReturn {
  approvalHistory: ApprovalHistoryRecord[];
  isLoading: boolean;
  error: string | null;
  fetchApprovalHistory: (documentId: string) => Promise<void>;
  clearHistory: () => void;
}

/**
 * Custom hook for fetching and managing approval history data
 *
 * This hook handles fetching approval history for a specific document
 * and provides loading states and error handling.
 *
 * @example
 * ```tsx
 * const { approvalHistory, isLoading, error, fetchApprovalHistory } = useApprovalHistory();
 *
 * // Fetch approval history for a document
 * useEffect(() => {
 *   if (documentId) {
 *     fetchApprovalHistory(documentId);
 *   }
 * }, [documentId, fetchApprovalHistory]);
 * ```
 */
export const useApprovalHistory = (): UseApprovalHistoryReturn => {
  const [approvalHistory, setApprovalHistory] = useState<ApprovalHistoryRecord[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const { entity } = useAuth();

  /**
   * Fetch approval history for a specific document
   */
  const fetchApprovalHistory = useCallback(
    async (documentId: string) => {
      if (!entity?.slug) {
        setError('Entity not found');
        return;
      }

      if (!documentId) {
        setApprovalHistory([]);
        setError(null);
        return;
      }

      setIsLoading(true);
      setError(null);

      try {
        const endpoint = `/entities/${entity.slug}/erp/${QUERY_KEYS.DON_HANG}/${documentId}/approval-history/`;
        console.log('useApprovalHistory: Fetching from endpoint:', endpoint);

        const response = await api.get<ApprovalHistoryResponse>(endpoint);
        console.log('useApprovalHistory: API response:', response.data);

        const results = response.data.results || [];
        console.log('useApprovalHistory: Setting approval history:', results);
        setApprovalHistory(results);
      } catch (error: any) {
        const errorMessage = error.response?.data?.error || error.message || 'Không thể tải lịch sử duyệt';
        setError(errorMessage);
        console.error('Error fetching approval history:', error);
        setApprovalHistory([]);
      } finally {
        setIsLoading(false);
      }
    },
    [entity?.slug]
  );

  /**
   * Clear approval history data
   */
  const clearHistory = useCallback(() => {
    setApprovalHistory([]);
    setError(null);
  }, []);

  return {
    approvalHistory,
    isLoading,
    error,
    fetchApprovalHistory,
    clearHistory
  };
};
