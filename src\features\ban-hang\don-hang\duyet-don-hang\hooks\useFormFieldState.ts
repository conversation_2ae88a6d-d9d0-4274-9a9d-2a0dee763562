import { useState } from 'react';
import {
  Account<PERSON><PERSON>l,
  Quyen<PERSON>hungTu,
  Han<PERSON>hanhToan,
  KhachHang,
  Nhan<PERSON>ien,
  <PERSON><PERSON>iTe,
  DonViCoSo,
  Dia<PERSON>hi,
  PhuongTienVan<PERSON>huyen,
  PhuongThucGiaoHang,
  PhuongThucThanhToan
} from '@/types/schemas';

export interface FormFieldState {
  // Customer information
  khachHang: KhachHang | null;
  nhanVien: NhanVien | null;
  hanThanhToan: HanThanhToan | null;

  // Document information
  quyenChungTu: QuyenChungTu | null;

  // Other information
  diaChi: DiaChi | null;
  phuongTienVanChuyen: PhuongTienVanChuyen | null;
  phuongTienGiaoHang: PhuongThucGiaoHang | null;
  phuongThucThanhToan: PhuongThucThanhToan | null;

  // So chung tu
  soChungTu: string;

  // Status
  status: string;
}

export interface FormFieldActions {
  // Search field setters
  setKhachHang: (khachHang: KhachHang) => void;
  setNhanVien: (nhanVien: NhanVien) => void;
  setHanThanhToan: (hanThanhToan: HanThanhToan) => void;
  setQuyenChungTu: (quyenChungTu: QuyenChungTu) => void;

  setDiaChi: (diaChi: DiaChi) => void;
  setPhuongTienVanChuyen: (phuongTienVanChuyen: PhuongTienVanChuyen) => void;
  setPhuongTienGiaoHang: (phuongTienGiaoHang: PhuongThucGiaoHang) => void;
  setPhuongThucThanhToan: (phuongThucThanhToan: PhuongThucThanhToan) => void;

  // So chung tu
  setSoChungTu: (soChungTu: string) => void;

  // Status
  setStatus: (status: string) => void;

  // Utility functions
  resetState: () => void;
  updateState: (updates: Partial<FormFieldState>) => void;
}

export interface UseFormFieldStateReturn {
  state: FormFieldState;
  actions: FormFieldActions;
}

const initialState: FormFieldState = {
  // Customer information
  khachHang: null,
  nhanVien: null,
  hanThanhToan: null,

  // Document information
  quyenChungTu: null,

  // Other information
  diaChi: null,
  phuongTienVanChuyen: null,
  phuongTienGiaoHang: null,
  phuongThucThanhToan: null,

  // So chung tu
  soChungTu: '',

  // Status
  status: '0'
};

function transformInitialData(initialData?: any): Partial<FormFieldState> {
  if (!initialData) return {};

  return {
    // Customer information
    khachHang: initialData.ma_kh_data || null,
    nhanVien: initialData.ma_nv_data || null,
    hanThanhToan: initialData.han_tt_data || null,

    // Document information
    quyenChungTu: initialData.so_ct_data || null,

    // Other information
    diaChi: initialData.ma_dc_data || null,
    phuongTienVanChuyen: initialData.ma_ptvc_data || null,
    phuongTienGiaoHang: initialData.ma_ptgh_data || null,
    phuongThucThanhToan: initialData.ma_pttt_data || null,

    // So chung tu
    soChungTu: initialData.so_ct || '',

    // Status
    status: initialData.status || '0'
  };
}

export function useFormFieldState(initialData?: any): UseFormFieldStateReturn {
  const [state, setState] = useState<FormFieldState>({
    ...initialState,
    ...transformInitialData(initialData)
  });

  const actions: FormFieldActions = {
    // Search field setters
    setKhachHang: (khachHang: KhachHang) => {
      setState(prev => ({
        ...prev,
        khachHang
      }));
    },

    setNhanVien: (nhanVien: NhanVien) => {
      setState(prev => ({
        ...prev,
        nhanVien
      }));
    },

    setHanThanhToan: (hanThanhToan: HanThanhToan) => {
      setState(prev => ({
        ...prev,
        hanThanhToan
      }));
    },

    setQuyenChungTu: (quyenChungTu: QuyenChungTu) => {
      setState(prev => ({
        ...prev,
        quyenChungTu
      }));
    },

    setDiaChi: (diaChi: DiaChi) => {
      setState(prev => ({
        ...prev,
        diaChi
      }));
    },

    setPhuongTienVanChuyen: (phuongTienVanChuyen: PhuongTienVanChuyen) => {
      setState(prev => ({
        ...prev,
        phuongTienVanChuyen
      }));
    },

    setPhuongTienGiaoHang: (phuongTienGiaoHang: PhuongThucGiaoHang) => {
      setState(prev => ({
        ...prev,
        phuongTienGiaoHang
      }));
    },

    setPhuongThucThanhToan: (phuongThucThanhToan: PhuongThucThanhToan) => {
      setState(prev => ({
        ...prev,
        phuongThucThanhToan
      }));
    },

    // So chung tu
    setSoChungTu: (soChungTu: string) => {
      setState(prev => ({ ...prev, soChungTu }));
    },

    // Status
    setStatus: (status: string) => {
      setState(prev => ({ ...prev, status }));
    },

    resetState: () => {
      setState(initialState);
    },

    updateState: (updates: Partial<FormFieldState>) => {
      setState(prev => ({ ...prev, ...updates }));
    }
  };

  return { state, actions };
}
