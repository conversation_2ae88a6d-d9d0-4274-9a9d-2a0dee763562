import { GridColDef } from '@mui/x-data-grid';
import React from 'react';
import { InputTable } from '@/components/custom/arito';
import { FormMode } from '@/types/form';

interface ApprovalInfoTabProps {
  formMode: FormMode;
  formState?: {
    state: any;
    actions: any;
  };
}

interface ApprovalInfo {
  id: number;
  thong_tin: string;
}

// Mock data cho thông tin duyệt theo thiết kế
const mockApprovalData: ApprovalInfo[] = [
  {
    id: 1,
    thong_tin: '1. Bạn Phó duyệt 2023_08_08 09:07:54:427 (Xử lý trong 1 phút)'
  },
  {
    id: 2,
    thong_tin: '2. Bạn <PERSON> duyệt 2023_08_08 09:07:54:427 (Xử lý trong 0 phút)'
  }
];

// Định nghĩa cột cho InputTable
const getApprovalInfoColumns = (): GridColDef[] => [
  {
    field: 'thong_tin',
    headerName: 'Thông tin',
    width: 600, // Cố định chiều rộng thay vì flex: 1
    renderCell: params => {
      const value = params.value as string;

      // Parse the string to extract components
      const match = value.match(/^(\d+)\.\s+Bạn\s+(.*?)\s+([\d_:\s]+)\s+(\(.*\))$/);

      if (match) {
        const [, number, action, datetime, duration] = match;
        return (
          <div className='flex flex-wrap items-center gap-1 py-2 text-sm'>
            <span className='font-medium text-gray-700'>{number}.</span>
            <span className='font-medium text-gray-900'>Bạn</span>
            <span className='font-medium text-red-600'>{action}</span>
            <span className='font-medium text-gray-900'>{datetime}</span>
            <span className='text-blue-600'>{duration}</span>
          </div>
        );
      }

      // Fallback if regex doesn't match
      return <span className='py-2 text-sm'>{value}</span>;
    }
  }
];

export const ApprovalInfoTab: React.FC<ApprovalInfoTabProps> = () => {
  return (
    <div className='p-4'>
      <div style={{ width: '100%', maxWidth: '650px' }}>
        <InputTable
          rows={mockApprovalData}
          columns={getApprovalInfoColumns()}
          getRowId={row => row.id.toString()}
          onRowClick={() => {}} // Không cần xử lý click
        />
      </div>
    </div>
  );
};
