import React from 'react';
import { QUERY_KEYS, accountSearchColumns, khachHangSearchColumns, nhanVienSearchColumns } from '@/constants';
import { DateRangeField, DocumentNumberRangeField } from '@/components/custom/arito/form/search-fields';
import { AccountModel, KhachHang, NhanVien } from '@/types/schemas';
import { SearchField, FormField } from '@/components/custom/arito';
import { Label } from '@/components/ui/label';

interface BasicInfoTabProps {
  formMode: 'add' | 'edit' | 'view';
}

const BasicInfoTab = ({ formMode }: BasicInfoTabProps) => {
  return (
    <div className='p-4'>
      {/* Ngày c/từ (từ/đến) */}
      <DateRangeField label='Ngày c/từ (từ/đến)' fromDateName='ngay_ct1' toDateName='ngay_ct2' />

      {/* <PERSON><PERSON> c/từ (từ/đến) */}
      <DocumentNumberRangeField formMode={formMode} label='Số c/từ (từ/đến)' fromName='so_ct1' toName='so_ct2' />

      {/* Loại hóa đơn */}
      <div className='flex items-center'>
        <Label className='w-[150px]'>Loại chứng từ</Label>
        <FormField
          name='ma_ngv'
          type='select'
          className='w-[200px]'
          label=''
          options={[
            { value: '', label: 'Tất cả' },
            { value: '5', label: 'Đơn hàng' },
            { value: '4', label: 'Hợp đồng' }
          ]}
          disabled={formMode === 'view'}
        />
      </div>

      <div className='flex items-center'>
        <Label className='w-[150px]'>Giao dịch</Label>
        <FormField
          name='ma_gd'
          type='select'
          className='w-[200px]'
          label=''
          options={[
            { value: '', label: 'Tất cả' },
            { value: 'DH', label: 'DH. Đơn hàng' },
            { value: 'HD', label: 'HD. Hợp đồng' }
          ]}
          disabled={formMode === 'view'}
        />
      </div>

      {/* Mã khách hàng */}
      <div className='flex items-center'>
        <Label className='w-[150px]'>Mã khách hàng</Label>
        <SearchField<KhachHang>
          type='text'
          name='ma_kh'
          searchEndpoint={`/${QUERY_KEYS.KHACH_HANG}/`}
          searchColumns={khachHangSearchColumns}
          columnDisplay='customer_code'
          displayRelatedField='customer_name'
          dialogTitle='Danh mục đối tượng'
          className='w-[205px]'
        />
      </div>

      {/* Mã nhân viên */}
      <div className='flex items-center'>
        <Label className='w-[150px]'>Mã nhân viên</Label>
        <SearchField<NhanVien>
          type='text'
          name='ma_nvbh'
          searchEndpoint={`/${QUERY_KEYS.TAI_KHOAN}/`}
          searchColumns={nhanVienSearchColumns}
          columnDisplay='ma_nhan_vien'
          displayRelatedField='ho_ten_nhan_vien'
          dialogTitle='Danh mục nhân viên bán hàng'
          className='w-[205px]'
        />
      </div>

      {/* Diễn giải */}
      <div className='flex items-center'>
        <Label className='w-[150px]'>Diễn giải</Label>
        <div className='w-full'>
          <FormField name='dien_giai' type='text' disabled={formMode === 'view'} />
        </div>
      </div>
    </div>
  );
};

export default BasicInfoTab;
