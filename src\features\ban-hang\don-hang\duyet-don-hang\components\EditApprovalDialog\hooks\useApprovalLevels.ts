import { useState, useEffect } from 'react';
import { DonHang } from '@/types/schemas/don-hang.type';
import { useUser } from '@/hooks/queries/useUser';

// Extract the approval level data type from DonHang schema
type ApprovalLevelData = NonNullable<DonHang['approval_level_data']>;
type ApprovalLevel = ApprovalLevelData['approve'][0] & {
  id: string;
  vaiTro?: string;
};

interface UseApprovalLevelsProps {
  initialData?: any;
}

export const useApprovalLevels = ({ initialData }: UseApprovalLevelsProps) => {
  const { users } = useUser();

  // Initialize followers state
  const [followers, setFollowers] = useState<string[]>(() => {
    const approvalData = initialData?.approval_level_data;
    return approvalData?.followers || [];
  });

  // Initialize approval levels with real data from initialData
  const [approvalLevels, setApprovalLevels] = useState<ApprovalLevel[]>(() => {
    const approvalData = initialData?.approval_level_data;
    console.log('Initial approval data:', approvalData);

    if (!approvalData || !approvalData.approve || !Array.isArray(approvalData.approve)) {
      // Return default structure if no data
      return [
        {
          id: '1',
          vaiTro: 'Vai trò, đối tượng duyệt',
          users: [],
          watchers: [],
          approved: false
        }
      ];
    }

    return approvalData.approve.map((approveLevel: ApprovalLevelData['approve'][0], index: number) => ({
      ...approveLevel,
      id: (index + 1).toString(),
      vaiTro: ''
    }));
  });

  // Update data when initialData changes
  useEffect(() => {
    if (initialData?.approval_level_data) {
      const approvalData = initialData.approval_level_data;
      console.log('Updated approval data:', approvalData);

      // Update approval levels
      if (approvalData.approve && Array.isArray(approvalData.approve)) {
        setApprovalLevels(
          approvalData.approve.map((approveLevel: ApprovalLevelData['approve'][0], index: number) => ({
            ...approveLevel,
            id: (index + 1).toString(),
            vaiTro: ''
          }))
        );
      }

      // Update followers
      if (approvalData.followers && Array.isArray(approvalData.followers)) {
        setFollowers(approvalData.followers);
      }
    }
  }, [initialData]);

  // Handler functions
  const handleAddApprovalLevel = () => {
    const newLevel: ApprovalLevel = {
      id: Date.now().toString(),
      vaiTro: 'Vai trò, đối tượng duyệt',
      users: [],
      watchers: [],
      approved: false
    };
    setApprovalLevels([...approvalLevels, newLevel]);
  };

  const handleRemoveApprovalLevel = (id: string) => {
    setApprovalLevels(approvalLevels.filter(level => level.id !== id));
  };

  const handleUpdateApprovalLevel = (id: string, field: keyof ApprovalLevel, value: any) => {
    setApprovalLevels(approvalLevels.map(level => (level.id === id ? { ...level, [field]: value } : level)));
  };

  // Get users value from approval level users array
  const getUsersValue = (level: ApprovalLevel) => {
    if (!level.users || !Array.isArray(level.users)) {
      return [];
    }

    // Return UUID strings directly since backend expects UUIDs
    return level.users.map(user => user.uuid).filter(uuid => uuid);
  };

  // Get watchers value from approval level watchers array
  const getWatchersValue = (level: ApprovalLevel) => {
    if (!level.watchers || !Array.isArray(level.watchers)) {
      return [];
    }

    // Return UUID strings directly since backend expects UUIDs
    return level.watchers.map(watcher => watcher.uuid).filter(uuid => uuid);
  };

  // Handler for updating approvers (multiselect)
  const handleUpdateApprovers = (levelId: string, selectedUserUuids: string[]) => {
    setApprovalLevels(
      approvalLevels.map(level =>
        level.id === levelId
          ? {
              ...level,
              users: selectedUserUuids.map(uuid => {
                const user = users.find(u => u.profile?.uuid === uuid || u.id.toString() === uuid);
                return {
                  uuid: uuid,
                  name: user
                    ? `${user.first_name || ''} ${user.last_name || ''}`.trim() || user.username || user.email
                    : ''
                };
              })
            }
          : level
      )
    );
  };

  // Handler for updating viewers (multiselect)
  const handleUpdateViewers = (levelId: string, selectedUserUuids: string[]) => {
    setApprovalLevels(
      approvalLevels.map(level =>
        level.id === levelId
          ? {
              ...level,
              watchers: selectedUserUuids.map(uuid => {
                const user = users.find(u => u.profile?.uuid === uuid || u.id.toString() === uuid);
                return {
                  uuid: uuid,
                  name: user
                    ? `${user.first_name || ''} ${user.last_name || ''}`.trim() || user.username || user.email
                    : ''
                };
              })
            }
          : level
      )
    );
  };

  // Handler for updating followers
  const handleUpdateFollowers = (selectedUserUuids: string[]) => {
    setFollowers(selectedUserUuids);
  };

  // Get followers value for FormField
  const getFollowersValue = () => {
    return followers;
  };

  // Generate API data format
  const getApiData = () => {
    return {
      approve: approvalLevels.map(level => ({
        users: level.users?.map(user => user.uuid) || [],
        watchers: level.watchers?.map(watcher => watcher.uuid) || []
      })),
      followers: followers
    };
  };

  return {
    approvalLevels,
    setApprovalLevels,
    followers,
    handleAddApprovalLevel,
    handleRemoveApprovalLevel,
    handleUpdateApprovalLevel,
    getUsersValue,
    getWatchersValue,
    handleUpdateApprovers,
    handleUpdateViewers,
    handleUpdateFollowers,
    getFollowersValue,
    getApiData
  };
};
