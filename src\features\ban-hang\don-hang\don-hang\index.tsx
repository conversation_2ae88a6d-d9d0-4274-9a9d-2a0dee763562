'use client';

import { useState } from 'react';
import Split from 'react-split';
import { InputTable, AritoDataTables, DeleteDialog, LoadingOverlay } from '@/components/custom/arito';
import { SearchDialog, ActionBar, FormDialog, InputTableAction } from './components';
import { getDataTableColumns, getInputTableColumns } from './cols-definition';
import { useFormState, useRows, useDonHang } from '@/hooks';
import { useLoading, useDonHangDetail } from './hooks';

export default function DonHangPage() {
  const [showSearchDialog, setShowSearchDialog] = useState(false);
  const { donHangs, isLoading, addDonHang, updateDonHang, deleteDonHang, refreshDonHangs } = useDonHang();
  const { selectedObj, selectedRowIndex, handleRowClick, clearSelection } = useRows();
  const {
    showForm,
    showDelete,
    formMode,
    isCopyMode,

    handleCloseForm,
    handleCloseDelete,
    handleAddClick,
    handleEditClick,
    handleViewClick,
    handleDeleteClick,
    handleCopyClick
  } = useFormState();
  const { loading } = useLoading({
    isOpen: showForm,
    external: false,
    duration: 500
  });
  const { detail, fetchDetail } = useDonHangDetail(selectedObj?.uuid);

  const handleSearch = () => {
    setShowSearchDialog(true);
  };

  const handleSearchClose = () => {
    setShowSearchDialog(false);
  };

  const handleSearchSubmit = (filters: any) => {
    console.log('Search filters:', filters);
    // TODO: Implement search functionality here
  };

  const handleSubmit = async (data: any) => {
    try {
      if (formMode === 'add') {
        await addDonHang(data);
      } else if (formMode === 'edit' && selectedObj) {
        await updateDonHang(selectedObj.uuid, data);
      }
      handleCloseForm();
      clearSelection();
    } catch (error) {
      console.error('Error submitting form:', error);
    }
  };

  const tables = [
    {
      name: 'Tất cả',
      rows: donHangs,
      columns: getDataTableColumns(handleViewClick)
    },
    {
      name: 'Lập chứng từ',
      rows: donHangs.filter(row => row.status === '0'),
      columns: getDataTableColumns(handleViewClick),
      icon: <div className='mr-2 size-[7px] rounded-full bg-[#EAD1DC]' />
    },
    {
      name: 'Chờ duyệt',
      rows: donHangs.filter(row => row.status === '1'),
      columns: getDataTableColumns(handleViewClick),
      icon: <div className='mr-2 size-[7px] rounded-full bg-red-500' />
    },
    {
      name: 'Đã duyệt',
      rows: donHangs.filter(row => row.status === '5'),
      columns: getDataTableColumns(handleViewClick),
      icon: <div className='mr-2 size-[7px] rounded-full bg-[#3D85C6]' />
    },
    {
      name: 'Khác',
      rows: donHangs.filter(row => row.status === '99'),
      columns: getDataTableColumns(handleViewClick),
      icon: <div className='mr-2 size-[7px] rounded-full bg-black' />
    }
  ];

  return (
    <div className='flex h-full min-h-[calc(100vh-120px)] w-full flex-col overflow-hidden'>
      {showSearchDialog && (
        <SearchDialog open={showSearchDialog} onClose={handleSearchClose} onSearch={handleSearchSubmit} />
      )}

      {loading && (
        <div className='absolute inset-0 z-50 flex items-center justify-center bg-white bg-opacity-80'>
          <LoadingOverlay />
        </div>
      )}

      {showForm && (
        <FormDialog
          formMode={formMode}
          open={showForm}
          onClose={handleCloseForm}
          initialData={
            selectedObj && (formMode === 'edit' || formMode === 'view' || (formMode === 'add' && isCopyMode))
              ? selectedObj
              : formMode === 'add' && !isCopyMode
                ? undefined
                : undefined
          }
          onSubmit={handleSubmit}
          onAdd={handleAddClick}
          onEdit={handleEditClick}
          onDelete={handleDeleteClick}
          onCopy={handleCopyClick}
        />
      )}

      {showDelete && (
        <DeleteDialog
          open={showDelete}
          onClose={handleCloseDelete}
          selectedObj={selectedObj}
          deleteObj={deleteDonHang}
          clearSelection={clearSelection}
        />
      )}

      {!showForm && (
        <>
          <ActionBar
            onAdd={handleAddClick}
            onEdit={handleEditClick}
            onDelete={handleDeleteClick}
            onCopy={handleCopyClick}
            onSearch={handleSearch}
            onRefresh={async () => {
              await refreshDonHangs();
              await fetchDetail();
            }}
            isEditDisabled={!selectedObj}
          />

          {isLoading && (
            <div className='flex h-full items-center justify-center'>
              <LoadingOverlay />
            </div>
          )}

          {!isLoading && (
            <Split
              className='flex flex-1 flex-col overflow-hidden'
              direction='vertical'
              sizes={[50, 50]}
              minSize={200}
              gutterSize={4}
              gutterAlign='center'
              snapOffset={30}
              dragInterval={1}
              cursor='row-resize'
            >
              <div className='w-full overflow-hidden'>
                <AritoDataTables
                  tables={tables}
                  onRowClick={handleRowClick}
                  selectedRowId={selectedRowIndex || undefined}
                />
              </div>

              <div className='max-h-[300px] overflow-hidden'>
                <InputTable
                  rows={detail || []}
                  columns={getInputTableColumns()}
                  mode={formMode}
                  actionButtons={
                    <InputTableAction
                      formMode={formMode}
                      handleExport={() => console.log('Export clicked')}
                      handlePin={() => console.log('Pin clicked')}
                    />
                  }
                />
              </div>
            </Split>
          )}
        </>
      )}
    </div>
  );
}
