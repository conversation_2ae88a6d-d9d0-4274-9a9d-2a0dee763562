'use client';

import { useWatch } from 'react-hook-form';
import { useState, useMemo } from 'react';
import { receiptVoucherSchema, receiptVoucherInitialValues, ReceiptVoucherFormData } from '../../schema';
import { calculateTotals, transformFormData, getFormTitle, getFormSubTitle } from '../../utils';
import { ExchangeRateTab, PaymentInfoTab, DetailsTab, OtherTab } from './tabs';
import { useInputTableRows } from '@/hooks/use-input-table-rows';
import { PhieuThuInput, PhieuThuChiTiet } from '@/types/schemas';
import { AritoForm } from '@/components/custom/arito';
import { useSearchFieldStates } from '../../hooks';
import { useAuth } from '@/contexts/auth-context';
import { BasicInfoTab } from './BasicInfoTab';
import { FormMode } from '@/types/form';
import { BottomBar } from './BottomBar';

interface PhieuThuFormProps {
  formMode: FormMode;
  initialData?: any;
  onSubmit: (data: PhieuThuInput) => void;
  onClose: () => void;
  currentIndex?: number;
  totalCount?: number;
}

export const PhieuThuForm: React.FC<PhieuThuFormProps> = ({
  formMode,
  initialData,
  onSubmit,
  onClose,
  currentIndex,
  totalCount
}) => {
  const [editExchangeRate, setEditExchangeRate] = useState(false);
  const { entityUnit } = useAuth();
  const searchFieldStates = useSearchFieldStates(initialData);
  const {
    rows,
    selectedRowUuid,
    handleRowClick,
    handleAddRow,
    handleDeleteRow,
    handleCopyRow,
    handlePasteRow,
    handleMoveRow,
    handleCellValueChange
  } = useInputTableRows<PhieuThuChiTiet>(initialData?.child_data);

  const calculatedTotals = useMemo(() => {
    return calculateTotals(rows);
  }, [rows]);

  const BottomBarWrapper = () => {
    const ty_gia = useWatch({ name: 'ty_gia' }) || '1';
    const exchangeRate = parseFloat(ty_gia);

    return (
      <BottomBar
        totalAmount={calculatedTotals.t_tien}
        totalAmountNT={calculatedTotals.t_tien_nt}
        formMode={formMode}
        ty_gia={exchangeRate}
      />
    );
  };

  const handleFormSubmit = async (formData: ReceiptVoucherFormData) => {
    const data = transformFormData(formData, searchFieldStates, rows, calculatedTotals, entityUnit, formMode);
    if (formMode === 'edit') {
      onSubmit?.({ ...data, unit_id: entityUnit?.uuid, i_so_ct: initialData?.i_so_ct });
    } else {
      onSubmit?.({ ...data, unit_id: entityUnit?.uuid, ngay_lct: new Date().toISOString().split('T')[0] });
    }
  };
  if (formMode === 'add' && initialData) {
    initialData = {
      ...initialData,
      ngay_ct: new Date().toISOString().split('T')[0],
      ngay_lct: new Date().toISOString().split('T')[0]
    };
  }
  return (
    <AritoForm<ReceiptVoucherFormData>
      title={getFormTitle(formMode)}
      subTitle={getFormSubTitle(currentIndex, totalCount)}
      mode={formMode}
      initialData={initialData || receiptVoucherInitialValues}
      onSubmit={handleFormSubmit}
      schema={receiptVoucherSchema}
      headerFields={<BasicInfoTab formMode={formMode} searchFieldStates={searchFieldStates} />}
      tabs={[
        {
          id: 'details',
          label: 'Chi tiết',
          component: (
            <DetailsTab
              rows={rows}
              selectedRowUuid={selectedRowUuid}
              handleRowClick={handleRowClick}
              handleAddRow={handleAddRow}
              handleDeleteRow={handleDeleteRow}
              handleCopyRow={handleCopyRow}
              handlePasteRow={handlePasteRow}
              handleMoveRow={handleMoveRow}
              handleCellValueChange={handleCellValueChange}
              formMode={formMode}
            />
          )
        },
        {
          id: 'payment-info',
          label: 'Thông tin thanh toán',
          component: <PaymentInfoTab formMode={formMode} searchFieldStates={searchFieldStates} />
        },
        {
          id: 'exchange-rate',
          label: 'Tỷ giá',
          component: (
            <ExchangeRateTab
              formMode={formMode}
              editExchangeRate={editExchangeRate}
              onEditExchangeRateChange={setEditExchangeRate}
            />
          )
        },
        {
          id: 'other',
          label: 'Khác',
          component: <OtherTab formMode={formMode} rows={rows} />
        }
      ]}
      onClose={onClose}
      bottomBar={<BottomBarWrapper />}
    />
  );
};
