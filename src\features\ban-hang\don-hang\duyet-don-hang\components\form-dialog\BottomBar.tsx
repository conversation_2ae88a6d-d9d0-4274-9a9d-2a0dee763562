import { FormField } from '@/components/custom/arito';
import { formatNumber } from '@/lib/formatUtils';
import { Label } from '@/components/ui/label';
import { calculateTotals } from '../../utils';
import { FormFieldState } from '../../hooks';

interface BottomBarProps {
  detailRows: any[];
  state: FormFieldState;
}

export function BottomBar({ detailRows, state }: BottomBarProps) {
  const totals = calculateTotals(detailRows);
  return (
    <div className='w-full border-t bg-white px-4 py-2'>
      <div className='flex'>
        <div className='flex flex-col'>
          <div className='flex items-center'>
            <Label className='w-28 font-medium'>Tổng số lượng</Label>
            <div className='w-[100px] pb-1'>
              <FormField name='t_so_luong' type='number' disabled value={formatNumber(totals.t_so_luong)} />
            </div>
          </div>
        </div>

        <div className='ml-4 flex flex-col'>
          <div className='flex items-center'>
            <Label className='w-24 font-medium'>Tổng tiền</Label>
            <FormField
              name='t_tien_nt2'
              type='number'
              disabled={true}
              labelClassName='mr-2 font-medium'
              inputClassName='text-right'
              value={formatNumber(totals.t_tien_nt2)}
            />
            <FormField
              name='t_tien2'
              type='number'
              disabled={true}
              className='hidden'
              labelClassName='mr-2 font-medium'
              inputClassName='text-right'
              value={formatNumber(totals.t_tien2)}
            />
          </div>
          <div className='flex items-center'>
            <Label className='w-24 font-medium'>Tổng thuế</Label>
            <FormField
              name='t_thue_nt'
              type='number'
              disabled={true}
              labelClassName='mr-2 font-medium'
              inputClassName='text-right'
              value={formatNumber(totals.t_thue_nt)}
            />
            <FormField
              name='t_thue'
              type='number'
              disabled={true}
              className='hidden'
              labelClassName='mr-2 font-medium'
              inputClassName='text-right'
              value={formatNumber(totals.t_thue)}
            />
          </div>
        </div>

        <div className='ml-auto flex w-1/3 flex-col'>
          <div className='flex items-center'>
            <Label className='w-32 font-medium'>Tổng tiền KM</Label>
            <FormField
              name='t_km_nt'
              type='number'
              disabled={true}
              labelClassName='mr-2 font-medium'
              inputClassName='text-right'
              value={formatNumber(totals.t_km_nt)}
            />
            <FormField
              name='t_km'
              type='number'
              disabled={true}
              className='hidden'
              labelClassName='mr-2 font-medium'
              inputClassName='text-right'
              value={formatNumber(totals.t_km)}
            />
          </div>
          <div className='flex items-center'>
            <Label className='w-32 font-medium'>Tổng chiết khấu</Label>
            <FormField
              name='t_ck_nt'
              type='number'
              disabled={true}
              labelClassName='mr-2 font-medium'
              inputClassName='text-right'
              value={formatNumber(totals.t_ck_nt)}
            />
            <FormField
              name='t_ck'
              type='number'
              disabled={true}
              className='hidden'
              labelClassName='mr-2 font-medium'
              inputClassName='text-right'
              value={formatNumber(totals.t_ck)}
            />
          </div>
        </div>
        <div className='ml-auto flex w-1/3 flex-col'>
          <div className='flex items-center'>
            <Label className='w-32 font-medium'>Tổng tiền hàng</Label>
            <FormField
              name='t_tc_tien_nt2'
              type='number'
              disabled={true}
              labelClassName='mr-2 font-medium'
              inputClassName='text-right'
              value={formatNumber(totals.t_tc_tien_nt2)}
            />
            <FormField
              name='t_tc_tien2'
              type='number'
              disabled={true}
              className='hidden'
              labelClassName='mr-2 font-medium'
              inputClassName='text-right'
              value={formatNumber(totals.t_tc_tien2)}
            />
          </div>
          <div className='flex items-center'>
            <Label className='w-32 font-medium'>Tổng thanh toán</Label>
            <FormField
              name='t_tt_nt'
              type='number'
              disabled={true}
              labelClassName='mr-2 font-medium'
              inputClassName='text-right'
              value={formatNumber(totals.t_tt_nt)}
            />
            <FormField
              name='t_tt'
              type='number'
              disabled={true}
              className='hidden'
              labelClassName='mr-2 font-medium'
              inputClassName='text-right'
              value={formatNumber(totals.t_tt)}
            />
          </div>
        </div>
      </div>
    </div>
  );
}
