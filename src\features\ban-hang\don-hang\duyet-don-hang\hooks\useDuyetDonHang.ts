import { useState } from 'react';
import { QUERY_KEYS } from '@/constants/query-keys';
import { useAuth } from '@/contexts/auth-context';
import api from '@/lib/api';

interface SubmitForProcessingData {
  action: 'submit';
  cnote?: string;
}

interface ApproveData {
  action: 'approve';
  cnote: string;
}

interface RejectData {
  action: 'refuse';
  cnote: string;
}

interface UnapproveData {
  action: 'unapprove';
  cnote: string;
}

interface BypassData {
  action: 'bypass';
  cnote?: string;
}

interface ApiResponse {
  success: boolean;
  message: string;
  action: string;
  cnote?: string;
  xprocess?: string;
  xstatus?: string;
  status?: string;
  previous_status?: string;
  current_status?: string;
  timestamp: string;
  user: string;
}

interface UpdateApprovalLevelData {
  approve: Array<{
    users: string[];
    watchers: string[];
  }>;
  followers: string[];
}

interface UseDuyetDonHangReturn {
  isLoading: boolean;
  error: string | null;
  submitForProcessing: (documentId: string, cnote?: string) => Promise<boolean>;
  approveDocument: (documentId: string, cnote: string, action?: 'approve' | 'skip') => Promise<boolean>;
  rejectDocument: (documentId: string, cnote: string, action?: 'cancel' | 'reject') => Promise<boolean>;
  unapproveDocument: (documentId: string, cnote?: string) => Promise<boolean>;
  updateApprovalLevel: (documentId: string, data: UpdateApprovalLevelData) => Promise<boolean>;
}

/**
 * Custom hook for handling document approval workflow with API integration
 * Integrates with the Don Hang approval system API endpoints
 *
 * Uses QUERY_KEYS.DON_HANG for consistent endpoint naming with useDonHang hook
 * to avoid DRY (Don't Repeat Yourself) violations.
 *
 * Status mapping:
 * - 0: lập chứng từ/nháp
 * - 1: chờ duyệt/chờ xử lý
 * - 4: đang duyệt/xử lý
 * - 5: đã duyệt/hoàn thành
 * - 9: đóng
 *
 * xstatus (sent in API payload):
 * - 3: approved (from approve endpoint)
 * - 4: unapproved (from unapprove endpoint)
 * - null: refused (from refuse endpoint, status becomes 9)
 */
export const useDuyetDonHang = (): UseDuyetDonHangReturn => {
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const { entity } = useAuth();

  const handleApiCall = async (
    endpoint: string,
    data: SubmitForProcessingData | ApproveData | RejectData | UnapproveData | BypassData
  ): Promise<boolean> => {
    if (!entity?.slug) {
      setError('Entity not found');
      return false;
    }

    setIsLoading(true);
    setError(null);

    try {
      console.log(`Making API call to: ${endpoint}`);

      const response = await api.post<ApiResponse>(endpoint, data);

      // Check if response is successful (either success: true or 200 status)
      if (response.data.success || response.status === 200) {
        console.log(`${data.action} successful:`, response.data.message || 'Operation completed');
        return true;
      } else {
        setError(response.data.message || 'Operation failed');
        console.error(`${data.action} failed:`, response.data);
        return false;
      }
    } catch (err: any) {
      const errorMessage =
        err.response?.data?.error || err.response?.data?.errors || err.message || 'An error occurred';

      // Handle validation errors
      if (err.response?.data?.errors) {
        const errors = err.response.data.errors;
        const errorMessages = Object.entries(errors)
          .map(
            ([field, messages]: [string, any]) =>
              `${field}: ${Array.isArray(messages) ? messages.join(', ') : messages}`
          )
          .join('; ');
        setError(errorMessages);
      } else {
        setError(typeof errorMessage === 'string' ? errorMessage : JSON.stringify(errorMessage));
      }

      console.error(`Error in ${data.action}:`, err);
      return false;
    } finally {
      setIsLoading(false);
    }
  };

  /**
   * Submit document for processing (status: 0 → 1)
   */
  const submitForProcessing = async (documentId: string, cnote?: string): Promise<boolean> => {
    const endpoint = `/entities/${entity?.slug}/erp/${QUERY_KEYS.DON_HANG}/${documentId}/submit-for-processing/`;
    const data: SubmitForProcessingData = {
      action: 'submit',
      ...(cnote && { cnote })
    };

    return handleApiCall(endpoint, data);
  };

  /**
   * Approve document (xprocess: APPROVE, xstatus: 3)
   * For 'skip' action, it will use bypass endpoint (superuser only)
   */
  const approveDocument = async (
    documentId: string,
    cnote: string,
    action: 'approve' | 'skip' = 'approve'
  ): Promise<boolean> => {
    if (action === 'skip') {
      // Use bypass endpoint for skip action (superuser only)
      const endpoint = `/entities/${entity?.slug}/erp/${QUERY_KEYS.DON_HANG}/${documentId}/bypass/`;
      const data: BypassData = {
        action: 'bypass',
        ...(cnote && { cnote })
      };
      return handleApiCall(endpoint, data);
    } else {
      // Regular approve
      const endpoint = `/entities/${entity?.slug}/erp/${QUERY_KEYS.DON_HANG}/${documentId}/approve/`;
      const data: ApproveData = {
        action: 'approve',
        cnote
      };
      return handleApiCall(endpoint, data);
    }
  };

  /**
   * Reject document (xprocess: REFUSE, status: 9)
   * Both 'cancel' and 'reject' actions use the refuse endpoint
   */
  const rejectDocument = async (
    documentId: string,
    cnote: string,
    action: 'cancel' | 'reject' = 'reject'
  ): Promise<boolean> => {
    const endpoint = `/entities/${entity?.slug}/erp/${QUERY_KEYS.DON_HANG}/${documentId}/refuse/`;
    const data: RejectData = {
      action: 'refuse',
      cnote
    };

    return handleApiCall(endpoint, data);
  };

  /**
   * Unapprove document - Undo approval (xprocess: UNAPPROVE, xstatus: 4)
   */
  const unapproveDocument = async (documentId: string, cnote?: string): Promise<boolean> => {
    const endpoint = `/entities/${entity?.slug}/erp/${QUERY_KEYS.DON_HANG}/${documentId}/unapprove/`;
    const data: UnapproveData = {
      action: 'unapprove',
      cnote: cnote || 'Bỏ lệnh duyệt đơn hàng'
    };

    return handleApiCall(endpoint, data);
  };

  /**
   * Update approval level configuration for a document
   * Uses the level-approval endpoint to update approval workflow
   */
  const updateApprovalLevel = async (documentId: string, data: UpdateApprovalLevelData): Promise<boolean> => {
    if (!entity?.slug) {
      setError('Entity not found');
      return false;
    }

    setIsLoading(true);
    setError(null);

    try {
      const endpoint = `/entities/${entity.slug}/erp/${QUERY_KEYS.DON_HANG}/${documentId}/level-approval/`;
      const response = await api.post(endpoint, data);

      // Check if response is successful
      if (response.status === 200) {
        return true;
      } else {
        setError('Failed to update approval level');
        console.error('Update approval level failed:', response);
        return false;
      }
    } catch (err: any) {
      const errorMessage =
        err.response?.data?.error ||
        err.response?.data?.errors ||
        err.message ||
        'An error occurred while updating approval level';

      // Handle validation errors
      if (err.response?.data?.errors) {
        const errors = err.response.data.errors;
        const errorMessages = Object.entries(errors)
          .map(
            ([field, messages]: [string, any]) =>
              `${field}: ${Array.isArray(messages) ? messages.join(', ') : messages}`
          )
          .join('; ');
        setError(errorMessages);
      } else {
        setError(typeof errorMessage === 'string' ? errorMessage : JSON.stringify(errorMessage));
      }

      console.error('Error updating approval level:', err);
      return false;
    } finally {
      setIsLoading(false);
    }
  };

  return {
    isLoading,
    error,
    submitForProcessing,
    approveDocument,
    rejectDocument,
    unapproveDocument,
    updateApprovalLevel
  };
};
