import { Lo<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>n, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Trash, Save, Send, Check, X, Undo } from 'lucide-react';
import React from 'react';
import { AritoActionButton } from '@/components/custom/arito';
import { FormMode } from '@/types/form';

/**
 * Get the title for the form dialog based on form mode and active tab
 */
export const getFormTitle = (formMode: FormMode, activeTab: string) => {
  if (formMode !== 'view') {
    return formMode === 'add' ? 'Mới' : 'Sửa';
  }

  switch (activeTab) {
    case 'info':
      return 'Đơn hàng';
    case 'history':
      return '<PERSON>ịch sử';
    default:
      return 'Đơn hàng';
  }
};

/**
 * Get the action buttons for the form dialog based on form mode and active tab
 */
export const getFormActionButtons = (
  formMode: FormMode,
  activeTab: string,
  handlers: {
    onAdd?: () => void;
    onEdit?: () => void;
    onDelete?: () => void;
    onCopy?: () => void;
    handleClose: () => void;
    onSendForProcessing?: () => void;
    onApprove?: () => void;
    onReject?: () => void;
    onUndo?: () => void;
  },
  status?: string
) => {
  const { onAdd, onEdit, onDelete, onCopy, handleClose, onSendForProcessing, onApprove, onReject, onUndo } = handlers;

  if (formMode !== 'view') {
    return null;
  }

  switch (activeTab) {
    case 'info':
      return (
        <>
          {/* Show "Gửi xử lý" button when status is '0' (Lập chứng từ) */}
          {status === '0' && <AritoActionButton title='Gửi xử lý' icon={Send} onClick={onSendForProcessing} />}

          {/* Show "Duyệt" and "Từ chối" buttons when status is '1' (Chờ duyệt) */}
          {status === '1' && (
            <>
              <AritoActionButton title='Duyệt' icon={Check} onClick={onApprove} />
              <AritoActionButton title='Từ chối' icon={X} onClick={onReject} />
            </>
          )}

          {/* Show "Bỏ lệnh" button when status is '5' (Đã duyệt) or '9' (Đóng) */}
          {(status === '5' || status === '9') && <AritoActionButton title='Bỏ lệnh' icon={Undo} onClick={onUndo} />}

          {/* <AritoActionButton title='Thêm' icon={Plus} onClick={onAdd} /> */}
          <AritoActionButton title='Sửa' icon={Pencil} onClick={onEdit} />
          <AritoActionButton title='Xóa' icon={Trash} onClick={onDelete} />
          {/* <AritoActionButton title='Sao chép' icon={Copy} onClick={onCopy} /> */}
          <AritoActionButton title='Đóng' icon={LogOut} onClick={handleClose} />
        </>
      );
    case 'history':
      return (
        <>
          <AritoActionButton
            title='Refresh'
            variant='secondary'
            icon={RefreshCcw}
            onClick={() => console.log('refresh')}
          />
          <AritoActionButton title='Cố định cột' variant='secondary' icon={Pin} onClick={() => console.log('pin')} />
          <AritoActionButton title='Đóng' variant='secondary' icon={LogOut} onClick={handleClose} />
        </>
      );
    default:
      return (
        <>
          <AritoActionButton title='Lưu' icon={Save} type='submit' />
          <AritoActionButton title='Đóng' icon={LogOut} onClick={handleClose} />
        </>
      );
  }
};
