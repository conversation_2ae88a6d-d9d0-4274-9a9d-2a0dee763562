import { format } from 'date-fns';
import { z } from 'zod';

export const searchSchema = z.object({
  // Date range fields
  ngay_ct1: z.string().optional(),
  ngay_ct2: z.string().optional(),
  // Document number range fields
  so_ct1: z.string().optional(),
  so_ct2: z.string().optional(),
  // Basic info fields
  ma_kh: z.string().optional(),
  ma_nvbh: z.string().optional(),
  dien_giai: z.string().optional(),
  // Status and filter fields
  status: z.string().optional(),
  ma_unit: z.string().optional(),
  user_id0: z.string().optional()
});

export type SearchFormValues = z.infer<typeof searchSchema>;

export const formSchema = z.object({
  ma_ngv: z.string().optional(),
  ten_kh: z.string().optional(),
  dia_chi: z.string().optional(),
  ma_so_thue: z.string().optional(),
  ong_ba: z.string().optional(),
  e_mail: z.string().optional(),
  dien_giai: z.string().optional(),
  ngay_hl: z.string().optional(),
  ma_nvbh: z.string().optional(),
  tk: z.string().optional(),
  ma_tt: z.string().optional(),
  so_ct: z.string().optional(),
  ngay_ct: z.string().optional(),
  ngay_lct: z.string().optional(),
  ma_nt: z.string().optional(),
  ty_gia: z.number().optional(),
  status: z.string().optional()
});

export type FormSchema = z.infer<typeof formSchema>;

export const initialFormValues: FormSchema = {
  ma_ngv: '5',
  ten_kh: '',
  dia_chi: '',
  ma_so_thue: '',
  ong_ba: '',
  e_mail: '',
  dien_giai: '',
  ngay_hl: '',
  ma_nvbh: '',
  tk: '',
  ma_tt: '',
  so_ct: '',
  ngay_ct: format(new Date(), 'yyyy-MM-dd'),
  ngay_lct: format(new Date(), 'yyyy-MM-dd'),
  ma_nt: 'VND',
  ty_gia: 1,
  status: '0'
};
