/**
 * TypeScript interface for DonHang (Sales Order) model
 *
 * This interface represents the structure of the DonHangModel from the backend.
 * It defines sales orders used for managing sales transactions.
 */

import { PhuongThucThanhToan } from './phuong-thuc-thanh-toan.type';
import { Dia<PERSON>hi<PERSON>hanHang } from './dia-chi-nhan-hang.type';
import { HanThanhToan } from './han-thanh-toan.type';
import { QuyenChungTu } from './quyen-chung-tu.type';
import { KenhBanHang } from './kenh-ban-hang.type';
import { KhachHang } from './khach-hang.type';
import { NhanVien } from './nhan-vien.type';
import { NgoaiTe } from './ngoai-te.type';
import { KhoHang } from './kho-hang.type';
import { ApiResponse } from '../api.type';

/**
 * Interface for DonHang (Sales Order) model
 * Based on the backend DonHangModel structure
 */
export interface DonHang {
  /**
   * Unique identifier (UUID)
   */
  uuid: string;

  /**
   * Reference to the entity model
   */
  entity_model: string;

  /**
   * Reference to the customer (ma_kh in backend)
   */
  ma_kh: string | KhachHang;

  /**
   * Customer data (read-only)
   */
  ma_kh_data?: KhachHang;

  /**
   * Tax identification number (ma_so_thue in backend)
   */
  ma_so_thue: string;

  /**
   * Customer name for tax purposes (ten_kh_thue in backend)
   */
  ten_kh_thue: string;

  /**
   * Contact person (nguoi_lien_he in backend)
   */
  nguoi_lien_he?: string | null;

  /**
   * Address (dia_chi in backend)
   */
  dia_chi: string;

  /**
   * Email address (e_mail in backend)
   */
  e_mail?: string | null;

  /**
   * Phone number (dien_thoai in backend)
   */
  dien_thoai?: string | null;

  /**
   * Reference to the sales employee (ma_nvbh in backend)
   */
  ma_nvbh: string | NhanVien;

  /**
   * Sales employee data (read-only)
   */
  ma_nvbh_data?: NhanVien;

  /**
   * Reference to the payment terms (ma_tt in backend)
   */
  ma_tt: string | HanThanhToan;

  /**
   * Payment terms data (read-only)
   */
  ma_tt_data?: HanThanhToan;

  /**
   * Description/explanation (dien_giai in backend)
   */
  dien_giai: string;

  /**
   * Document number (so_ct in backend)
   */
  so_ct: string;

  /**
   * Reference to document book (ma_nk in backend)
   */
  ma_nk: string | QuyenChungTu;

  /**
   * Document book data (read-only)
   */
  ma_nk_data?: QuyenChungTu;

  /**
   * Document date (ngay_ct in backend)
   */
  ngay_ct: string;

  /**
   * Document creation date (ngay_lct in backend)
   */
  ngay_lct: string;

  /**
   * Delivery date (ngay_giao in backend)
   */
  ngay_giao: string;

  /**
   * Reference to the currency (ma_nt in backend)
   */
  ma_nt: string | NgoaiTe;

  /**
   * Currency data (read-only)
   */
  ma_nt_data?: NgoaiTe;

  /**
   * Exchange rate (ty_gia in backend)
   */
  ty_gia: number;

  /**
   * Status (status in backend)
   */
  status: string;

  /**
   * Reference to the warehouse (ma_kho in backend)
   */
  ma_kho: string | KhoHang;

  /**
   * Warehouse data (read-only)
   */
  ma_kho_data?: KhoHang;

  /**
   * Reference to the payment method (ma_pttt in backend)
   */
  ma_pttt: string | PhuongThucThanhToan;

  /**
   * Payment method data (read-only)
   */
  ma_pttt_data?: PhuongThucThanhToan;

  /**
   * Reference to the delivery address (ma_dcnh in backend)
   */
  ma_dcnh: string | DiaChiNhanHang;

  /**
   * Delivery address data (read-only)
   */
  ma_dcnh_data?: DiaChiNhanHang;

  /**
   * Reference to sales channel (ma_kbh in backend)
   */
  ma_kbh: string | KenhBanHang;

  /**
   * Sales channel data (read-only)
   */
  ma_kbh_data?: KenhBanHang;

  /**
   * Transaction type (ma_gd in backend)
   */
  ma_gd: string;

  /**
   * Notes (ghi_chu in backend)
   */
  ghi_chu?: string | null;

  /**
   * Total quantity (t_so_luong in backend)
   */
  t_so_luong: number;

  /**
   * Total amount in foreign currency (t_tien_nt in backend)
   */
  t_tien_nt: number;

  /**
   * Total amount (t_tien in backend)
   */
  t_tien: number;

  /**
   * Total tax in foreign currency (t_thue_nt in backend)
   */
  t_thue_nt: number;

  /**
   * Total tax (t_thue in backend)
   */
  t_thue: number;

  /**
   * Total payment in foreign currency (t_tt_nt in backend)
   */
  t_tt_nt: number;

  /**
   * Total payment (t_tt in backend)
   */
  t_tt: number;

  /**
   * Transfer flag (transfer_yn in backend)
   */
  transfer_yn: boolean;

  /**
   * Timestamp of creation
   */
  created?: string;

  /**
   * Timestamp of last update
   */
  updated?: string;

  /**
   * Approval level data containing approval workflow information
   */
  approval_level_data?: {
    uuid: string;
    approve: Array<{
      users: Array<{
        uuid: string;
        name: string;
      }>;
      watchers: Array<{
        uuid: string;
        name: string;
      }>;
      approved: boolean;
    }>;
    followers: Array<{
      uuid: string;
      name: string;
    }>;
    created: string;
    updated: string;
  };
}

/**
 * Type for DonHang API response
 */
export type DonHangResponse = ApiResponse<DonHang>;

/**
 * Type for creating or updating a DonHang
 */
export interface DonHangInput {
  /**
   * Required system fields
   */
  ma_ngv?: string;
  unit_id?: string;
  i_so_ct?: string;

  /**
   * Reference to the customer
   */
  ma_kh: string;

  /**
   * Tax identification number
   */
  ma_so_thue: string;

  /**
   * Customer name for tax purposes
   */
  ten_kh_thue: string;

  /**
   * Contact person (optional)
   */
  nguoi_lien_he?: string | null;

  /**
   * Address
   */
  dia_chi: string;

  /**
   * Email address (optional)
   */
  e_mail?: string | null;

  /**
   * Phone number (optional)
   */
  dien_thoai?: string | null;

  /**
   * Reference to the sales employee
   */
  ma_nvbh: string;

  /**
   * Reference to the payment terms
   */
  ma_tt: string;

  /**
   * Account reference
   */
  tk?: string;

  /**
   * Description/explanation
   */
  dien_giai: string;

  /**
   * Document number
   */
  so_ct: string;

  /**
   * Reference to document book
   */
  ma_nk: string;

  /**
   * Document date
   */
  ngay_ct: string;

  /**
   * Document creation date
   */
  ngay_lct: string;

  /**
   * Delivery date
   */
  ngay_giao: string;

  /**
   * Reference to the currency
   */
  ma_nt: string;

  /**
   * Exchange rate
   */
  ty_gia: number;

  /**
   * Status
   */
  status?: string;

  /**
   * Reference to the warehouse
   */
  ma_kho: string;

  /**
   * Reference to the payment method
   */
  ma_pttt: string;

  /**
   * Reference to the delivery address
   */
  ma_dcnh: string;

  /**
   * Reference to sales channel
   */
  ma_kbh: string;

  /**
   * Transaction type
   */
  ma_gd: string;

  /**
   * Notes (optional)
   */
  ghi_chu?: string | null;

  /**
   * Total quantity
   */
  t_so_luong: number;

  /**
   * Total amount in foreign currency
   */
  t_tien_nt: number;

  /**
   * Total amount
   */
  t_tien: number;

  /**
   * Total tax in foreign currency
   */
  t_thue_nt: number;

  /**
   * Total tax
   */
  t_thue: number;

  /**
   * Total payment in foreign currency
   */
  t_tt_nt: number;

  /**
   * Total payment
   */
  t_tt: number;

  /**
   * Transfer flag
   */
  transfer_yn?: boolean;
}
