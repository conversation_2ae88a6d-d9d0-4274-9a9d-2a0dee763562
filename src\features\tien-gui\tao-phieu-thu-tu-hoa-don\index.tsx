'use client';

import { useState } from 'react';

import { useRouter } from 'next/navigation';
import { LoadingOverlay, AritoDataTables } from '@/components/custom/arito';
import { getDataTableColumnsCreate } from './cols-definition';
import { useFormState, useReport, useRows } from '@/hooks';
import { FormDialog, ActionBar } from './components';
import { QUERY_KEYS } from '@/constants';

export default function TaoPhieuThuTuHoaDonPage() {
  const router = useRouter();
  const [showSearchDialog, setShowSearchDialog] = useState(false);
  const { data, getReport, isLoading, totalItems, currentPage, handlePageChange, refreshData } = useReport<any, any>({
    endpoint: QUERY_KEYS.TAO_PHIEU_THU_TU_HOA_DON
  });
  const [selectedInvoices, setSelectedInvoices] = useState<string[]>([]);
  const [documentType, setDocumentType] = useState<string>(''); // Store ct value
  const [searchParams, setSearchParams] = useState<any>(null); // Store search parameters
  const { selectedRowIndex, handleRowClick } = useRows();
  const { showForm } = useFormState();
  const handleSearch = () => {
    setShowSearchDialog(true);
  };

  const handleSearchClose = () => {
    setShowSearchDialog(false);
  };

  const handleSearchSubmit = async (data: any) => {
    try {
      await getReport(data);
      // Store the ct value from form data for later use
      if (data.ct) {
        setDocumentType(data.ct);
      }
      // Store search parameters for ActionBar
      setSearchParams(data);
      setShowSearchDialog(false);
    } catch (error) {
      console.error('Error submitting form:', error);
    }
  };

  // Hàm xử lý khi checkbox được tick/untick
  const handleCheckboxChange = (invoiceUuid: string, field: string, newValue: boolean) => {
    let updatedSelectedInvoices: string[];

    if (newValue) {
      updatedSelectedInvoices = [...selectedInvoices, invoiceUuid];
      setSelectedInvoices(updatedSelectedInvoices);
    } else {
      updatedSelectedInvoices = selectedInvoices.filter(id => id !== invoiceUuid);
      setSelectedInvoices(updatedSelectedInvoices);
    }
  };
  const onCreateClick = () => {
    // Determine URL based on document type (ct field)
    let url = `/${QUERY_KEYS.PHIEU_THU}`;

    if (documentType === 'HD1') {
      url += `?hdbh=${selectedInvoices.join(',')}`;
    } else if (documentType === 'HD2') {
      url += `?hdbdv=${selectedInvoices.join(',')}`;
    } else {
      // Default fallback to HD2 behavior if no document type is set
      url += `?hdbdv=${selectedInvoices.join(',')}`;
    }

    router.push(url);
  };

  const onDeleteClick = () => {
    // Handle delete receipt logic here
    console.log('Delete receipts for invoices:', selectedInvoices);
    // TODO: Implement delete receipt functionality
  };

  const tables = [
    {
      name: 'Tất cả',
      rows: data,
      columns: getDataTableColumnsCreate(handleCheckboxChange, selectedInvoices)
    }
  ];

  return (
    <div className='flex h-full min-h-[calc(100vh-120px)] w-full flex-col overflow-hidden'>
      {showSearchDialog && (
        <FormDialog open={showSearchDialog} onClose={handleSearchClose} onSubmit={handleSearchSubmit} />
      )}

      {!showForm && (
        <>
          <ActionBar
            onSearchClick={handleSearch}
            onRefreshClick={refreshData}
            onCreateReceiptClick={onCreateClick}
            onDeleteReceiptClick={onDeleteClick}
            searchParams={searchParams}
            totalItems={totalItems}
            selectedCount={selectedInvoices.length}
          />

          {isLoading && (
            <div className='flex h-full items-center justify-center'>
              <LoadingOverlay />
            </div>
          )}

          {!isLoading && (
            <div className='w-full overflow-hidden'>
              <AritoDataTables
                tables={tables}
                onRowClick={handleRowClick}
                totalItems={totalItems}
                currentPage={currentPage}
                onPageChange={handlePageChange}
                serverSidePagination={true}
                selectedRowId={selectedRowIndex || undefined}
              />
            </div>
          )}
        </>
      )}
    </div>
  );
}
