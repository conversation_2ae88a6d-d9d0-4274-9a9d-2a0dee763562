import React from 'react';
import { createReceiptFromInvoiceSchema, initialValues, CreateReceiptFromInvoiceFormValues } from '../schema';
import { AritoForm, AritoDialog, AritoIcon, BottomBar, LoadingOverlay } from '@/components/custom/arito';
import { BasicInfo } from './BasicInfo';
import { useDonViCoSo } from '@/hooks';

interface FormDialogProps {
  open: boolean;
  onClose: () => void;
  onSubmit: (values: CreateReceiptFromInvoiceFormValues) => void;
}

export const FormDialog: React.FC<FormDialogProps> = ({ open, onClose, onSubmit }) => {
  const handleSubmit = (data: CreateReceiptFromInvoiceFormValues) => {
    onSubmit(data);
  };
  const { donViCoSos, isLoading } = useDonViCoSo();

  return (
    <>
      {isLoading && <LoadingOverlay />}
      {!isLoading && (
        <AritoDialog
          open={open}
          onClose={onClose}
          title='Tạo phiếu thu từ hóa đơn'
          maxWidth='md'
          disableBackdropClose={false}
          disableEscapeKeyDown={false}
          titleIcon={<AritoIcon icon={12} />}
        >
          <AritoForm
            mode='add'
            hasAritoActionBar={false}
            schema={createReceiptFromInvoiceSchema}
            initialData={{ ...initialValues, unit_id: donViCoSos[0]?.uuid || '' }}
            onSubmit={handleSubmit}
            className='w-[800px]'
            headerFields={
              <div className='max-h-[calc(100vh-150px)] w-full overflow-y-auto'>
                <BasicInfo />
              </div>
            }
            classNameBottomBar='relative w-full flex justify-end gap-2 px-2'
            bottomBar={<BottomBar mode='add' onClose={onClose} />}
          />
        </AritoDialog>
      )}
    </>
  );
};
