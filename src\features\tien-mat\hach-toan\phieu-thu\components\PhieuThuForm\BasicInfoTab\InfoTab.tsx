import { SearchField, FormField, DocumentNumberField, CurrencyInput } from '@/components/custom/arito';
import { InfoTabSearchFieldStates } from '../../../hooks/useSearchFieldStates';
import { accountSearchColumns } from '@/constants/search-columns';
import { MA_CHUNG_TU, QUERY_KEYS } from '@/constants';
import { Label } from '@/components/ui/label';
import { <PERSON><PERSON><PERSON>an } from '@/types/schemas';
import { FormMode } from '@/types/form';

interface InfoTabProps {
  formMode: FormMode;
  searchFieldStates: InfoTabSearchFieldStates;
}

export const InfoTab = ({ formMode, searchFieldStates }: InfoTabProps) => {
  return (
    <div className='px-7 py-4'>
      <div className='flex flex-col space-y-2 lg:flex-row lg:space-x-1 lg:space-y-0'>
        {/* Left column - takes remaining width */}
        <div className='flex-1 space-y-2'>
          <div className='flex items-center lg:grid-cols-[120px,1fr]'>
            <Label className='w-36 text-sm font-medium'>Loại phiếu thu</Label>
            <div className='w-[250px]'>
              <FormField
                name='ma_ngv'
                type='select'
                options={[
                  { value: '1', label: '1. Thu theo hóa đơn' },
                  { value: '2', label: '2. Thu theo đối tượng' },
                  { value: '3', label: '3. Thu khác' }
                ]}
                disabled={formMode === 'view'}
              />
            </div>
          </div>
          <div className='flex items-center'>
            <Label className='w-36 text-sm font-medium'>Địa chỉ</Label>
            <div className='flex-1'>
              <FormField type='text' name='dia_chi' disabled={formMode === 'view'} />
            </div>
          </div>
          <div className='flex items-center'>
            <Label className='w-36 text-sm font-medium'>Người nộp tiền</Label>
            <div className='flex-1'>
              <FormField type='text' name='ong_ba' disabled={formMode === 'view'} />
            </div>
          </div>
          <div className='flex items-center'>
            <Label className='w-36 text-sm font-medium'>Diễn giải</Label>
            <div className='flex-1'>
              <FormField type='text' name='dien_giai' disabled={formMode === 'view'} />
            </div>
          </div>
          <div className='flex items-center'>
            <Label className='w-36 text-sm font-medium'>Tài khoản nợ</Label>
            <SearchField<TaiKhoan>
              type='text'
              disabled={formMode === 'view'}
              dialogTitle='Danh mục tài khoản'
              searchEndpoint={`/${QUERY_KEYS.TAI_KHOAN}`}
              searchColumns={accountSearchColumns}
              displayRelatedField='name'
              columnDisplay='code'
              value={searchFieldStates.account?.code || ''}
              relatedFieldValue={searchFieldStates.account?.name || ''}
              onRowSelection={searchFieldStates.setAccount}
            />
          </div>
        </div>

        {/* Right column - fixed width of 15rem */}
        <div className='mr-3 w-full space-y-2 lg:w-80'>
          <DocumentNumberField
            ma_ct={MA_CHUNG_TU.TIEN_MAT.PHIEU_THU}
            quyenChungTu={searchFieldStates.quyenChungTu}
            onQuyenChungTuChange={searchFieldStates.setQuyenChungTu}
            soChungTu={searchFieldStates.soChungTu}
            onSoChungTuChange={searchFieldStates.setSoChungTu}
            formMode={formMode}
            labelClassName='w-36 font-medium'
          />
          <div className='flex items-center'>
            <Label className='w-36 text-sm font-medium'>Ngày chứng từ</Label>
            <FormField type='date' name='ngay_ct' disabled={formMode === 'view'} />
          </div>
          <div className='flex items-center'>
            <Label className='w-36 text-left text-sm font-medium'>Ngày lập chứng từ</Label>
            <FormField type='date' name='ngay_lct' disabled={formMode === 'view'} />
          </div>
          <CurrencyInput formMode={formMode} classNameInput='w-full' labelClassName='w-36' />
          <div className='flex items-center'>
            <Label className='w-36 text-sm font-medium'>Trạng thái</Label>
            <FormField
              type='select'
              name='status'
              disabled={formMode === 'view'}
              options={[
                { value: '0', label: 'Chưa ghi sổ' },
                { value: '3', label: 'Chờ duyệt' },
                { value: '5', label: 'Đã ghi sổ' }
              ]}
            />
          </div>
          <div className='flex items-center'>
            <div></div>
            <div className='flex items-center gap-2'>
              <FormField type='checkbox' name='transfer_yn' disabled={formMode === 'view'} />
              <Label className='w-36 text-sm font-medium'>Dữ liệu được nhận</Label>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};
