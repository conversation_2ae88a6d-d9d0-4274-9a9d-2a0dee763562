import {
  QUERY_KEYS,
  MA_CHUNG_TU,
  hanThanhToanSearchColumns,
  khachHangSearchColumns,
  nhanVienSearchColumns
} from '@/constants';
import { SearchField, FormField, AritoIcon, DocumentNumberField, CurrencyInput } from '@/components/custom/arito';
import { HanThanhToan, KhachHang, NhanVien } from '@/types/schemas';
import { FormFieldState, FormFieldActions } from '../../hooks';
import { Label } from '@/components/ui/label';
import { FormMode } from '@/types/form';

interface BasicInfoTabProps {
  formMode: FormMode;
  formState: {
    state: FormFieldState;
    actions: FormFieldActions;
  };
}

export function BasicInfoTab({ formMode, formState: { state, actions } }: BasicInfoTabProps) {
  return (
    <div className='flex flex-col gap-3 p-4'>
      {/* Customer Information Section */}
      <div className='grid grid-cols-12 gap-6'>
        {/* Left Column */}
        <div className='col-span-6 space-y-1'>
          <div className='flex items-center'>
            <Label className='w-[195px]'>Loại đơn hàng</Label>
            <div className='w-[205px]'>
              <FormField
                name='ma_ngv'
                type='select'
                disabled={formMode === 'view'}
                options={[
                  { value: '5', label: 'Đơn hàng' },
                  { value: '4', label: 'Hợp đồng' }
                ]}
              />
            </div>
          </div>

          <div className='flex items-center'>
            <Label className='w-[195px]'>Mã khách hàng</Label>
            <SearchField<KhachHang>
              type='text'
              name='ma_kh'
              value={state.khachHang?.customer_code || ''}
              searchEndpoint={`/${QUERY_KEYS.KHACH_HANG}/`}
              searchColumns={khachHangSearchColumns}
              onRowSelection={actions.setKhachHang}
              columnDisplay='customer_code'
              dialogTitle='Danh mục khách hàng'
              className='w-[205px]'
              disabled={formMode === 'view'}
            />

            <div className='ml-4 flex items-center'>
              <Label className='w-20'>Mã số thuế</Label>
              <FormField
                name='ma_so_thue'
                type='text'
                disabled={formMode === 'view'}
                placeholder='Nhập và tra cứu'
                value={state.khachHang?.tax_code || ''}
              />
              <button className='ml-2 flex items-center justify-center border border-gray-200 px-3 py-1.5'>
                <AritoIcon icon={15} className='shrink-0' />
              </button>
              <button className='ml-2 flex items-center justify-center border border-gray-200 px-3 py-1.5'>
                <AritoIcon icon={888} />
              </button>
            </div>
          </div>

          <div className='flex items-center'>
            <Label className='w-[195px]'>Tên khách hàng</Label>
            <div className='flex-1'>
              <FormField
                name='ten_kh'
                type='text'
                disabled={formMode === 'view'}
                value={state.khachHang?.customer_name || ''}
                placeholder='Nhập tên khách hàng/đơn vị'
              />
            </div>
          </div>

          <div className='flex items-center'>
            <Label className='w-[195px]'>Địa chỉ</Label>
            <div className='flex-1'>
              <FormField
                name='dia_chi'
                type='text'
                disabled={formMode === 'view'}
                labelClassName='w-48'
                value={state.khachHang?.address || ''}
                placeholder='Nhập địa chỉ khách hàng'
              />
            </div>
          </div>

          <div className='flex items-center'>
            <Label className='w-[195px]'>Mã nhân viên</Label>
            <SearchField<NhanVien>
              type='text'
              name='ma_nvbh'
              value={state.nhanVien?.ma_nhan_vien || state.khachHang?.sales_rep_data?.ma_nhan_vien || ''}
              relatedFieldValue={
                state.nhanVien?.ho_ten_nhan_vien || state.khachHang?.sales_rep_data?.ho_ten_nhan_vien || ''
              }
              searchEndpoint={`/${QUERY_KEYS.NHAN_VIEN}/`}
              columnDisplay='ma_nhan_vien'
              displayRelatedField='ho_ten_nhan_vien'
              searchColumns={nhanVienSearchColumns}
              dialogTitle='Danh sách nhân viên bán hàng'
              onRowSelection={actions.setNhanVien}
              disabled={formMode === 'view'}
            />
          </div>

          <div className='flex items-center'>
            <Label className='w-[195px]'>Diễn giải</Label>
            <div className='flex-1'>
              <FormField name='dien_giai' type='text' disabled={formMode === 'view'} />
            </div>
          </div>

          <div className='flex items-center'>
            <Label className='w-[195px]'>Ngày hiệu lực</Label>
            <div className='w-[200px]'>
              <FormField name='ngay_hl' type='date' disabled={formMode === 'view'} />
            </div>
          </div>
        </div>

        {/* Middle Column */}
        <div className='col-span-3 space-y-1'>
          <div className='h-8'></div>
          <div className='h-8'></div>

          <div className='ml-4 flex items-center'>
            <FormField
              name='ong_ba'
              label='Người nhận'
              labelClassName='w-32'
              inputClassName='w-6 text-red-600'
              disabled={formMode === 'view'}
              className='w-[350px]'
              value={state.khachHang?.contact_person || ''}
              placeholder='Nhập tên người liên hệ'
            />
          </div>

          <div className='ml-4 flex items-center'>
            <FormField
              name='e_mail'
              label='Email'
              labelClassName='w-32'
              inputClassName='w-64'
              className='w-[350px]'
              disabled={formMode === 'view'}
              value={state.khachHang?.email || ''}
              placeholder='Nhập email'
            />
          </div>

          <div className='ml-4 flex items-center'>
            <div className='flex items-center'>
              <Label className='w-[132px]'>Hạn thanh toán</Label>
              <SearchField<HanThanhToan>
                type='text'
                name='ma_tt'
                value={state.hanThanhToan?.ten_tt || state.khachHang?.payment_term_data?.ten_tt || ''}
                searchEndpoint={`/${QUERY_KEYS.HAN_THANH_TOAN}/`}
                columnDisplay='ma_tt'
                searchColumns={hanThanhToanSearchColumns}
                dialogTitle='Danh mục hạn thanh toán'
                className='w-[230px]'
                onRowSelection={actions.setHanThanhToan}
                placeholder='Hạn thanh toán'
                disabled={formMode === 'view'}
              />
            </div>
          </div>
        </div>

        {/* Right Column */}
        <div className='col-span-3 flex'>
          {/* Tab Content */}
          <div className='flex-1 pr-2'>
            <DocumentNumberField
              ma_ct={MA_CHUNG_TU.BAN_HANG.DON_HANG}
              quyenChungTu={state.quyenChungTu}
              onQuyenChungTuChange={actions.setQuyenChungTu}
              soChungTu={state.soChungTu}
              onSoChungTuChange={actions.setSoChungTu}
              disabled={formMode === 'view'}
              classNameSearchField='w-full'
            />

            <FormField
              label='Ngày chứng từ'
              name='ngay_ct'
              type='date'
              disabled={formMode === 'view'}
              labelClassName='w-32 shrink-0'
            />

            <FormField
              label='Số hợp đồng'
              name='so_ct2'
              type='text'
              disabled={formMode === 'view'}
              labelClassName='w-32 shrink-0'
            />

            <CurrencyInput formMode={formMode} classNameInput='w-full' />

            <FormField
              name='status'
              type='select'
              disabled={formMode === 'view'}
              label='Trạng thái'
              labelClassName='w-32 shrink-0'
              inputClassName='w-full'
              options={[
                { value: '0', label: 'Lập chứng từ' },
                { value: '1', label: 'Chờ duyệt' },
                { value: '4', label: 'Đang duyệt' },
                { value: '5', label: 'Đã duyệt' },
                { value: '9', label: 'Đóng' }
              ]}
              value={state.status}
              onValueChange={actions.setStatus}
            />
            <div className='mt-1 flex'>
              <div className='mb-4 h-2 w-32 shrink-0' />
              <FormField
                label='Dữ liệu nhận được'
                name='transfer_yn'
                type='checkbox'
                disabled={true}
                labelClassName='w-32'
                defaultValue={false}
              />
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
