import { useState } from 'react';
import { QUERY_KEYS, soCaiColumns, theKhoColumns, thanhToanPhaiThuColumns, thueVatDauRaColumns } from '@/constants';
import TableDialog from '@/components/custom/arito/info-dropdown/data-tables';
import HistoryDialog from '@/components/custom/arito/info-dropdown/history';
import { AritoIcon, AritoMenuButton } from '@/components/custom/arito';

const InfoDropdown = ({ uuid }: { uuid: string }) => {
  const [formOpen, setFormOpen] = useState('none');

  return (
    <>
      <AritoMenuButton
        className='mr-3 bg-transparent px-5 py-0 text-2xl text-gray-950 hover:bg-slate-200'
        icon={<>𝒾</>}
        items={[
          {
            title: 'Thông tin người tạo sửa',
            icon: <AritoIcon icon={428} />,
            onClick: () => setFormOpen('history'),
            group: 0
          },
          {
            title: 'Xem sổ kho mua/bán hàng',
            icon: <AritoIcon icon={0} />,
            onClick: () => setFormOpen('xem-so-kho'),
            group: 1
          },
          {
            title: 'Xem sổ cái',
            icon: <AritoIcon icon={0} />,
            onClick: () => setFormOpen('xem-so-cai'),
            group: 1
          },
          {
            title: 'Thanh toán phải thu',
            icon: <AritoIcon icon={0} />,
            onClick: () => setFormOpen('thanh-toan-phai-thu'),
            group: 1
          },
          {
            title: 'Thuế VAT đầu ra',
            icon: <AritoIcon icon={0} />,
            onClick: () => setFormOpen('thue-vat-dau-ra'),
            group: 1
          },
          {
            title: 'Thông tin chứng từ',
            icon: <AritoIcon icon={0} />,
            onClick: () => setFormOpen('thong-tin-chung-tu'),
            group: 2
          }
        ]}
      />
      {formOpen === 'history' && <HistoryDialog onClose={() => setFormOpen('none')} uuid={uuid} />}

      {(() => {
        const dialogConfigs = {
          'xem-so-kho': { title: 'Sổ kho mua/bán hàng', columns: theKhoColumns, endpoint: QUERY_KEYS.SO_KHO },
          'xem-so-cai': { title: 'Sổ cái', columns: soCaiColumns, endpoint: QUERY_KEYS.SO_CAI },
          'thanh-toan-phai-thu': {
            title: 'Thanh toán phải thu',
            columns: thanhToanPhaiThuColumns,
            endpoint: QUERY_KEYS.THANH_TOAN_PHAI_THU
          },
          'thue-vat-dau-ra': {
            title: 'Thuế VAT đầu ra',
            columns: thueVatDauRaColumns,
            endpoint: QUERY_KEYS.THUE_VAT_DAU_RA
          }
        };

        const config = dialogConfigs[formOpen as keyof typeof dialogConfigs];
        return config ? (
          <TableDialog
            title={config.title}
            uuid={uuid}
            columns={config.columns}
            endpoint={config.endpoint}
            onClose={() => setFormOpen('none')}
          />
        ) : null;
      })()}
    </>
  );
};

export default InfoDropdown;
