import React from 'react';
import { SearchField, FormField } from '@/components/custom/arito';
import { QUERY_KEYS, donViSearchColumns } from '@/constants';
import { Label } from '@/components/ui/label';
import { DonViCoSo } from '@/types/schemas';
import { FormMode } from '@/types/form';

interface FilterTabProps {
  formMode: FormMode;
}

const FilterTab = ({ formMode }: FilterTabProps) => {
  return (
    <div className='p-4'>
      {/* Đơn vị */}
      <div className='flex items-center'>
        <Label className='w-[150px]'>Đơn vị</Label>
        <SearchField<DonViCoSo>
          type='text'
          name='ma_unit'
          searchEndpoint={`/${QUERY_KEYS.DON_VI_CO_SO}/`}
          searchColumns={donViSearchColumns}
          columnDisplay='ma_unit'
          displayRelatedField='ten_unit'
          dialogTitle='<PERSON><PERSON> mục đơn vị'
          className='w-[205px]'
        />
      </div>

      {/* Trạng thái */}
      <div className='flex items-center'>
        <Label className='w-[150px]'>Trạng thái</Label>
        <FormField
          name='status'
          type='select'
          className='w-[200px]'
          options={[
            { value: '', label: 'Tất cả' },
            { value: '0', label: 'Lập chứng từ' },
            { value: '1', label: 'Chờ duyệt' },
            { value: '5', label: 'Xuất hóa đơn' },
            { value: '6', label: 'Đang thực hiện' },
            { value: '7', label: 'Hoàn thành' },
            { value: '9', label: 'Hủy' }
          ]}
          disabled={formMode === 'view'}
        />
      </div>

      {/* Lọc theo người SD */}
      <div className='flex items-center'>
        <Label className='w-[150px]'>Lọc theo người SD</Label>
        <FormField
          name='user_id0'
          type='select'
          className='w-[200px]'
          options={[
            { value: '', label: 'Tất cả' },
            { value: '1', label: 'Lọc theo người tạo' }
          ]}
          disabled={formMode === 'view'}
        />
      </div>
    </div>
  );
};

export default FilterTab;
