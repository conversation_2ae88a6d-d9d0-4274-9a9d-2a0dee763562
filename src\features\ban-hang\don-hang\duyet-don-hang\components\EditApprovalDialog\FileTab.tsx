import { FileSelectField } from '@/components/custom/arito/form/search-fields';
import { FormMode } from '@/types/form';

interface FileTabProps {
  formMode: FormMode;
  initialData?: any;
}

const FileTab: React.FC<FileTabProps> = ({ formMode, initialData }) => {
  const handleFileChange = (file: File | null) => {
    console.log('File changed:', file);
    // In real implementation, this would update form state
  };

  return (
    <div className='space-y-2 p-4'>
      <div className='space-y-1'>
        <FileSelectField
          label='Chọn files'
          formMode={formMode}
          onFileChange={handleFileChange}
          labelClassName='w-32 min-w-32'
          className='flex flex-col sm:flex-row sm:items-center'
        />
      </div>
    </div>
  );
};

export default FileTab;
