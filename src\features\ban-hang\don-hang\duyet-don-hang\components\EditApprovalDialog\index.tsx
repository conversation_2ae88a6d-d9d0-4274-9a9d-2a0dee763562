import React, { useState, useEffect, useRef } from 'react';
import { ApprovalInfoTab, ApprovalInfoTabRef } from './ApprovalInfoTab';
import { AritoForm, AritoHeaderTabs } from '@/components/custom/arito';
import { getFormTitle, getFormActionButtons } from '../../utils';
import { BasicInfoTab, BasicInfoTabRef } from './BasicInfoTab';
import { FormMode } from '@/types/form';
import FileTab from './FileTab';

interface EditApprovalDialogProps {
  open: boolean;
  onClose: () => void;
  onSubmit: (values: any) => Promise<void>;
  initialData?: any;
  formMode?: FormMode;
}

const EditApprovalDialog: React.FC<EditApprovalDialogProps> = ({
  open,
  onClose,
  onSubmit,
  initialData,
  formMode = 'edit'
}) => {
  const [activeTab, setActiveTab] = useState('info');
  const [isFormDirty, setIsFormDirty] = useState(false);

  // Shared state cho followers
  const [followersValue, setFollowersValue] = useState<string[]>(() => {
    return initialData?.approval_level_data?.followers || [];
  });

  // Refs để lấy dữ liệu từ các tabs
  const approvalInfoRef = useRef<ApprovalInfoTabRef>(null);
  const basicInfoRef = useRef<BasicInfoTabRef>(null);

  // Initialize form data when dialog opens
  useEffect(() => {
    if (!open) {
      setIsFormDirty(false);
      setActiveTab('info');
    }
  }, [open]);

  const handleSubmit = async (_data: any) => {
    try {
      const approvalData = {
        approve: approvalInfoRef.current?.getApiData() || [],
        followers: followersValue
      };

      await onSubmit(approvalData);
      onClose();
    } catch (error) {
      console.error('Error submitting approval form:', error);
    }
  };

  const handleClose = () => {
    if (isFormDirty) {
      // TODO: Show confirmation dialog for unsaved changes
      const confirmClose = window.confirm('Bạn có muốn đóng mà không lưu thay đổi?');
      if (!confirmClose) return;
    }

    setIsFormDirty(false);
    setActiveTab('info');
    onClose();
  };

  const handleTabChange = (tabId: string) => {
    setActiveTab(tabId);
  };

  if (!open) return null;

  const title = getFormTitle(formMode, activeTab);
  const actionButtons = getFormActionButtons(formMode, activeTab, {
    onAdd: () => {},
    onEdit: () => {},
    onDelete: () => {},
    onCopy: () => {},
    handleClose
  });

  return (
    <>
      <AritoForm
        mode={formMode}
        initialData={initialData}
        title={title}
        actionButtons={actionButtons}
        subTitle='Sửa đơn từ, thay đổi quy trình duyệt'
        onSubmit={handleSubmit}
        onClose={handleClose}
        headerFields={
          <div onChange={() => setIsFormDirty(true)}>
            <AritoHeaderTabs
              tabs={[
                {
                  id: 'info',
                  label: 'Thông tin',
                  component: (
                    <BasicInfoTab
                      ref={basicInfoRef}
                      formMode={formMode}
                      formState={{ state: initialData || {}, actions: {} }}
                      followersValue={followersValue}
                      onFollowersChange={setFollowersValue}
                    />
                  )
                }
              ]}
              onTabChange={handleTabChange}
              defaultTabIndex={activeTab === 'info' ? 0 : 0}
            />
          </div>
        }
        tabs={
          activeTab === 'info' && [
            {
              id: 'approval-info',
              label: 'Thông tin duyệt',
              component: <ApprovalInfoTab ref={approvalInfoRef} formMode={formMode} initialData={initialData} />
            },
            {
              id: 'file-attachments',
              label: 'File đính kèm',
              component: <FileTab formMode={formMode} initialData={initialData} />
            }
          ]
        }
      />
    </>
  );
};

export default EditApprovalDialog;
