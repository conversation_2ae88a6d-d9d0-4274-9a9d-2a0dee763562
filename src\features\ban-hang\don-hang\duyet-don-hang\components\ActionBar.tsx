import { Edit2, Pin, <PERSON>fresh<PERSON><PERSON>, Sheet } from 'lucide-react';
import { AritoActionButton, AritoMenuButton, AritoActionBar, AritoIcon } from '@/components/custom/arito';

interface ActionBarProps {
  onEditClick?: () => void;
  onRefreshClick?: () => void;
  onFixedColumnsClick?: () => void;
  onExportDataClick?: () => void;
  onApproveClick?: () => void;
  onReviewProgressClick?: () => void;
  onReviewConfigClick?: () => void;
  onHideShowFilterClick?: () => void;
  className?: string;
}

export default function ActionBar({
  onEditClick,
  onRefreshClick,
  onFixedColumnsClick,
  onExportDataClick,
  onApproveClick,
  onReviewProgressClick,
  onReviewConfigClick,
  onHideShowFilterClick,
  className
}: ActionBarProps) {
  return (
    <>
      <AritoActionBar className={className} titleComponent={<h1 className='text-xl font-bold'>Đ<PERSON>n từ bán hàng</h1>}>
        <AritoActionButton title='Sửa' icon={Edit2} onClick={onEditClick} variant='primary' />
        <AritoActionButton title='Refresh' icon={RefreshCw} onClick={onRefreshClick} variant='destructive' />
        <AritoActionButton title='Cố định cột' icon={Pin} onClick={onFixedColumnsClick} variant='secondary' />
        <AritoActionButton title='Kết xuất dữ liệu' icon={Sheet} onClick={onExportDataClick} variant='secondary' />

        <AritoMenuButton
          items={[
            {
              title: 'Duyệt nhiều đơn',
              icon: <AritoIcon icon={862} />,
              onClick: onApproveClick,
              group: 0
            },
            {
              title: 'Chuyển đổi người duyệt đang trong tiến trình duyệt',
              icon: <AritoIcon icon={377} />,
              onClick: onReviewProgressClick,
              group: 1
            },
            {
              title: 'Chuyển đổi người duyệt đã cấu hình',
              icon: <AritoIcon icon={382} />,
              onClick: onReviewConfigClick,
              group: 1
            },
            {
              title: 'Ẩn/hiển thị cây lọc tìm',
              icon: <AritoIcon icon={538} />,
              onClick: onHideShowFilterClick,
              group: 2
            }
          ]}
        />
      </AritoActionBar>
    </>
  );
}
