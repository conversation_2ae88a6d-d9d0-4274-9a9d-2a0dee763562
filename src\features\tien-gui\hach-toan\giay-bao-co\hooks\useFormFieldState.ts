import { useState, useEffect } from 'react';
import { <PERSON><PERSON><PERSON><PERSON>, QuyenChungTu, PhuongThucThanhToan, TaiKhoanNganHang } from '@/types/schemas';

export interface FormFieldState {
  taiKhoan: <PERSON>Khoan | null;
  quyenChungTu: QuyenChungTu | null;
  soChungTu: string;
  thanhToan: PhuongThucThanhToan | null;
  taiKhoanNganHang: TaiKhoanNganHang | null;
}

export interface FormFieldActions {
  setTaiKhoan: (taiKhoan: TaiKhoan) => void;
  setQuyenChungTu: (quyenChungTu: QuyenChungTu) => void;
  setSoChungTu: (soChungTu: string) => void;
  setThanhToan: (thanhToan: PhuongThucThanhToan) => void;
  setTaiKhoanNganHang: (taiKhoanNganHang: TaiKhoanNganHang) => void;

  resetState: () => void;
  updateState: (updates: Partial<FormFieldState>) => void;
}

export interface UseFormFieldStateReturn {
  state: FormFieldState;
  actions: FormFieldActions;
}

const initialState: FormFieldState = {
  taiKhoan: null,
  quyenChungTu: null,
  soChungTu: '',
  thanhToan: null,
  taiKhoanNganHang: null
};

function transformInitialData(initialData?: any): Partial<FormFieldState> {
  if (!initialData) return {};

  return {
    taiKhoan: initialData.tk_data || null,
    quyenChungTu: initialData.ma_nk_data || null,
    soChungTu: initialData.so_ct || '',
    thanhToan: initialData.ma_pttt_data || null,
    taiKhoanNganHang: initialData.tknh_data || null
  };
}

export function useFormFieldState(initialData?: any): UseFormFieldStateReturn {
  const [state, setState] = useState<FormFieldState>({
    ...initialState,
    ...transformInitialData(initialData)
  });

  // Update state when initialData changes
  useEffect(() => {
    if (initialData) {
      setState(prev => ({
        ...prev,
        ...transformInitialData(initialData)
      }));
    }
  }, [initialData]);

  const actions: FormFieldActions = {
    setTaiKhoan: (taiKhoan: TaiKhoan) => {
      setState(prev => ({
        ...prev,
        taiKhoan
      }));
    },

    setQuyenChungTu: (quyenChungTu: QuyenChungTu) => {
      setState(prev => ({
        ...prev,
        quyenChungTu
      }));
    },

    setSoChungTu: (soChungTu: string) => {
      setState(prev => ({
        ...prev,
        soChungTu
      }));
    },

    setThanhToan: (thanhToan: PhuongThucThanhToan) => {
      setState(prev => ({
        ...prev,
        thanhToan
      }));
    },

    setTaiKhoanNganHang: (taiKhoanNganHang: TaiKhoanNganHang) => {
      setState(prev => ({
        ...prev,
        taiKhoanNganHang
      }));
    },

    resetState: () => {
      setState(initialState);
    },

    updateState: (updates: Partial<FormFieldState>) => {
      setState(prev => ({ ...prev, ...updates }));
    }
  };

  return { state, actions };
}
