import { GridColDef, GridRenderCellParams } from '@mui/x-data-grid';
import { Checkbox } from '@/components/ui/checkbox';
import { formatMoney } from '@/lib/formatUtils';

export const documentTypeSearchColumns: GridColDef[] = [
  { field: 'ma_ct', headerName: 'Mã chứng từ', width: 150 },
  { field: 'ten_ct', headerName: 'Tên chứng từ', width: 300 }
];

export const customerSearchColumns: GridColDef[] = [
  { field: 'customer_code', headerName: 'Mã đối tượng', width: 150 },
  { field: 'customer_name', headerName: 'Tên đối tượng', width: 200 },
  { field: 'CongNoPhaiThu', headerName: 'Công nợ p/thu', width: 150 },
  { field: 'CongNoPhaiTra', headerName: 'Công nợ p/trả', width: 150 },
  { field: 'MaSoThue', headerName: 'Mã số thuế', width: 150 },
  { field: 'email', headerName: 'Email', width: 200 },
  { field: 'phone', headerName: 'Số điện thoại', width: 150 }
];

export const foreignCurrencySearchColumns: GridColDef[] = [
  { field: 'ma_nt', headerName: 'Mã ngoại tệ', width: 150 },
  { field: 'ten_nt', headerName: 'Tên ngoại tệ', width: 300 }
];

export const permissionSearchColumns: GridColDef[] = [
  { field: 'ma_quyen', headerName: 'Mã quyển', width: 150 },
  { field: 'ten_quyen', headerName: 'Tên quyền', width: 300 },
  { field: 'so_khai_bao', headerName: 'Số khai báo', width: 150 }
];

export const getDataTableColumnsCreate = (
  onCellValueChange: (rowId: string, field: string, newValue: any) => void = () => {},
  selectedInvoices: string[] = []
): GridColDef[] => [
  {
    field: 'selected',
    headerName: '',
    width: 50,
    type: 'boolean',
    sortable: false,
    filterable: false,
    disableColumnMenu: true,
    renderCell: (params: GridRenderCellParams) => {
      const invoiceUuid = params.row.uuid || params.row.id;
      const isChecked = selectedInvoices.includes(invoiceUuid);

      return (
        <Checkbox
          checked={isChecked}
          onCheckedChange={newValue => {
            onCellValueChange(invoiceUuid, 'selected', newValue);
          }}
        />
      );
    }
  },
  { field: 'so_ct', headerName: 'Số c/từ', width: 150, renderCell: params => params.row.so_ct3 || params.row.so_ct },
  { field: 'ngay_ct', headerName: 'Ngày c/từ', width: 120 },
  { field: 'ten_ngv', headerName: 'Loại hóa đơn', width: 150 },
  { field: 'ma_kh', headerName: 'Mã khách hàng', width: 150 },
  { field: 'ten_kh', headerName: 'Tên khách hàng', width: 250 },
  { field: 'tk', headerName: 'Tk nợ', width: 100 },
  { field: 'ma_nt', headerName: 'Ngoại tệ', width: 100 },
  { field: 't_tt_nt', headerName: 'Tổng tiền', width: 150 },
  { field: 'ten_ttct', headerName: 'Trạng thái', width: 120 },
  { field: 'ma_tthddt', headerName: 'Trạng thái HĐĐT', width: 150 },
  { field: 'ma_unit', headerName: 'Đơn vị', width: 150 }
];

export const getDataTableColumnsDelete = (
  onCellValueChange: (rowId: string, field: string, newValue: any) => void = () => {},
  selectedInvoices: string[] = []
): GridColDef[] => [
  {
    field: 'selected',
    headerName: '',
    width: 50,
    type: 'boolean',
    sortable: false,
    filterable: false,
    disableColumnMenu: true,
    renderCell: (params: GridRenderCellParams) => {
      // Use invoice_uuid (original UUID) instead of id (index) for selection
      const invoiceUuid = params.row.uuid;
      const isChecked = selectedInvoices.includes(invoiceUuid);

      return (
        <Checkbox
          checked={isChecked}
          onCheckedChange={newValue => onCellValueChange(invoiceUuid, 'selected', newValue)}
        />
      );
    }
  },
  { field: 'so_ct3', headerName: 'Số c/từ đã tạo', width: 150 },
  { field: 'ma_ct3', headerName: 'Mã c/từ đã tạo', width: 150 },
  { field: 'so_ct', headerName: 'Số c/từ', width: 120 },
  { field: 'ngay_ct', headerName: 'Ngày c/từ', width: 120 },
  { field: 'ten_ngv', headerName: 'Loại hóa đơn', width: 150 },
  { field: 'ma_kh', headerName: 'Mã khách hàng', width: 150 },
  { field: 'ten_kh', headerName: 'Tên khách hàng', width: 250 },
  { field: 'tk', headerName: 'Tk nợ', width: 100 },
  { field: 'ma_nt', headerName: 'Ngoại tệ', width: 100 },
  { field: 't_tt_nt', headerName: 'Tổng tiền', width: 150, renderCell: params => formatMoney(params.row.t_tt_nt) },
  { field: 'ten_ttct', headerName: 'Trạng thái', width: 120 },
  { field: 'ma_tthddt', headerName: 'Trạng thái HĐĐT', width: 150 },
  { field: 'ma_unit', headerName: 'Đơn vị', width: 150 }
];
