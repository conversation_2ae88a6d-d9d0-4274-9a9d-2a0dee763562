import type { GridColDef } from '@mui/x-data-grid';
import { Button } from '@mui/material';
import { AritoIcon, InputTable } from '@/components/custom/arito';
import { AritoDialog } from '@/components/custom/arito/dialog';
import InputTableAction from './InputTableAction';
import { QUERY_KEYS } from '@/constants';
import { useCRUD } from '@/hooks';

interface TableDialogProps {
  uuid: string;
  onClose: () => void;
  title: string;
  endpoint?: string;
  columns: GridColDef[];
}

const TableDialog = ({ onClose, uuid, columns, title, endpoint }: TableDialogProps) => {
  const { data, isLoading } = useCRUD<any, any>({
    endpoint: `${endpoint}/${uuid}`
  });

  return (
    <AritoDialog
      open={true}
      onClose={onClose}
      title={title}
      maxWidth='md'
      disableBackdropClose={false}
      disableEscapeKeyDown={true}
      showFullscreenToggle={false}
      titleIcon={<AritoIcon icon={260} />}
      actions={
        <Button onClick={onClose} variant='outlined'>
          <AritoIcon icon={885} className='mr-2' />
          Huỷ
        </Button>
      }
    >
      <div className='min-w-[55vw] space-y-3 pt-4'>
        <InputTable
          rows={isLoading ? [] : data}
          columns={columns}
          actionButtons={
            <InputTableAction
              handleRefresh={() => console.log('Refresh clicked')}
              handleEdit={() => console.log('Edit clicked')}
              handleExport={() => console.log('Export clicked')}
              handlePin={() => console.log('Pin clicked')}
            />
          }
        />
      </div>
    </AritoDialog>
  );
};

export default TableDialog;
