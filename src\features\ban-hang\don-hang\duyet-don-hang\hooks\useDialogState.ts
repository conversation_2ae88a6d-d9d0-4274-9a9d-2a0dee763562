import { useState } from 'react';

export interface UseDialogStateReturn {
  reviewProgressDialogOpen: boolean;
  reviewConfigDialogOpen: boolean;
  handleOpenReviewProgressDialog: () => void;
  handleCloseReviewProgressDialog: () => void;
  handleOpenReviewConfigDialog: () => void;
  handleCloseReviewConfigDialog: () => void;
}

export const useDialogState = (): UseDialogStateReturn => {
  const [reviewProgressDialogOpen, setReviewProgressDialogOpen] = useState(false);
  const [reviewConfigDialogOpen, setReviewConfigDialogOpen] = useState(false);

  const handleOpenReviewProgressDialog = () => {
    setReviewProgressDialogOpen(true);
  };

  const handleCloseReviewProgressDialog = () => {
    setReviewProgressDialogOpen(false);
  };

  const handleOpenReviewConfigDialog = () => {
    setReviewConfigDialogOpen(true);
  };

  const handleCloseReviewConfigDialog = () => {
    setReviewConfigDialogOpen(false);
  };

  return {
    reviewProgressDialogOpen,
    reviewConfigDialogOpen,
    handleOpenReviewProgressDialog,
    handleCloseReviewProgressDialog,
    handleOpenReviewConfigDialog,
    handleCloseReviewConfigDialog
  };
};
