import React, { forwardRef, useImper<PERSON><PERSON><PERSON>le } from 'react';
import { X, Plus } from 'lucide-react';
import { useApprovalLevels } from './hooks/useApprovalLevels';
import { FormField } from '@/components/custom/arito';
import { useUser } from '@/hooks/queries/useUser';
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import { FormMode } from '@/types/form';

interface ApprovalInfoTabProps {
  formMode: FormMode;
  initialData?: any;
}

export interface ApprovalInfoTabRef {
  getApiData: () => Array<{
    users: string[];
    watchers: string[];
  }>;
}

export const ApprovalInfoTab = forwardRef<ApprovalInfoTabRef, ApprovalInfoTabProps>(function ApprovalInfoTab(
  { formMode, initialData },
  ref
) {
  // Get users data
  const { users } = useUser();

  // Convert users to options format for FormField
  // Use profile.uuid as value since backend expects UUID strings
  const userOptions = users.map(user => ({
    value: user.profile?.uuid || user.id.toString(),
    label: `${user.first_name || ''} ${user.last_name || ''}`.trim() || user.username || user.email
  }));

  // Use custom hook for approval levels logic
  const {
    approvalLevels,
    handleAddApprovalLevel,
    handleRemoveApprovalLevel,
    handleUpdateApprovalLevel,
    getUsersValue,
    getWatchersValue,
    handleUpdateApprovers,
    handleUpdateViewers
  } = useApprovalLevels({ initialData });

  // Expose methods via ref
  useImperativeHandle(
    ref,
    () => ({
      getApiData: () => {
        console.log('ApprovalInfoTab - getApiData called, approvalLevels:', approvalLevels);
        const result = approvalLevels.map(level => ({
          users: level.users?.map(user => user.uuid) || [],
          watchers: level.watchers?.map(watcher => watcher.uuid) || []
        }));
        console.log('ApprovalInfoTab - getApiData result:', result);
        return result;
      }
    }),
    [approvalLevels]
  );

  return (
    <div className='max-h-[calc(100vh-300px)] overflow-y-auto p-4'>
      <div className='space-y-6'>
        {/* Dynamic Approval Levels */}
        {approvalLevels.map((level, index) => (
          <div key={level.id} className='space-y-3 rounded-lg border border-gray-200 p-4'>
            <div className='flex items-center justify-between'>
              <Label className='text-sm font-medium'>Người duyệt cấp {index + 1}</Label>
              {formMode !== 'view' && approvalLevels.length > 1 && (
                <Button
                  type='button'
                  variant='outline'
                  size='sm'
                  onClick={() => handleRemoveApprovalLevel(level.id)}
                  className='h-8 w-8 p-0 text-red-600 hover:bg-red-50'
                >
                  <X size={16} />
                </Button>
              )}
            </div>

            {/* Vai trò, đối tượng duyệt */}
            <div className='flex items-center'>
              <Label className='w-32 text-sm'>Vai trò, đối tượng duyệt</Label>
              <div className='flex-1'>
                <FormField
                  name={`vai_tro_${level.id}`}
                  type='text'
                  value={level.vaiTro}
                  onChange={e => handleUpdateApprovalLevel(level.id, 'vaiTro', (e.target as HTMLInputElement).value)}
                  disabled={formMode === 'view'}
                  className='w-full'
                  placeholder='Vai trò, đối tượng duyệt'
                />
              </div>
            </div>

            {/* Người duyệt */}
            <div className='flex items-start'>
              <Label className='mt-2 w-32 text-sm'>Người duyệt</Label>
              <div className='flex-1 space-y-2'>
                {/* Hiển thị danh sách users hiện tại */}
                {level.users && level.users.length > 0 && (
                  <div className='flex flex-wrap gap-1'>
                    {level.users.map((user, index) => (
                      <span key={index} className='rounded bg-blue-100 px-2 py-1 text-xs text-blue-800'>
                        {user.name}
                      </span>
                    ))}
                  </div>
                )}

                {/* FormField để chọn */}
                <FormField
                  name={`nguoi_duyet_${level.id}`}
                  type='multiselect'
                  value={getUsersValue(level)}
                  options={userOptions}
                  disabled={formMode === 'view'}
                  className='text-sm'
                  placeholder='Chọn người duyệt...'
                  onValueChange={(selectedIds: string[]) => handleUpdateApprovers(level.id, selectedIds)}
                />
              </div>
            </div>

            {/* Người xem */}
            <div className='flex items-start'>
              <Label className='mt-2 w-32 text-sm'>Người xem</Label>
              <div className='flex-1 space-y-2'>
                {/* Hiển thị danh sách watchers hiện tại */}
                {level.watchers && level.watchers.length > 0 && (
                  <div className='flex flex-wrap gap-1'>
                    {level.watchers.map((watcher, index) => (
                      <span key={index} className='rounded bg-green-100 px-2 py-1 text-xs text-green-800'>
                        {watcher.name}
                      </span>
                    ))}
                  </div>
                )}

                {/* FormField để chọn */}
                <FormField
                  name={`nguoi_xem_${level.id}`}
                  type='multiselect'
                  value={getWatchersValue(level)}
                  options={userOptions}
                  disabled={formMode === 'view'}
                  className='text-sm'
                  placeholder='Chọn người xem...'
                  onValueChange={(selectedIds: string[]) => handleUpdateViewers(level.id, selectedIds)}
                />
              </div>
            </div>
          </div>
        ))}

        {/* Add New Approval Level Button */}
        {formMode !== 'view' && (
          <div className='flex justify-center'>
            <Button
              type='button'
              variant='outline'
              onClick={handleAddApprovalLevel}
              className='flex items-center gap-2'
            >
              <Plus size={16} />
              Thêm cấp duyệt
            </Button>
          </div>
        )}
      </div>
    </div>
  );
});

ApprovalInfoTab.displayName = 'ApprovalInfoTab';
