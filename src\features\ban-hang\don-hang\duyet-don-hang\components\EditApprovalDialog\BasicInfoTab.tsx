import React, { forwardRef, useImperativeHandle } from 'react';
import { AritoColoredDot, FormField } from '@/components/custom/arito';
import { useUser } from '@/hooks/queries/useUser';
import { Label } from '@/components/ui/label';
import { FormMode } from '@/types/form';

interface BasicInfoTabProps {
  formMode: FormMode;
  formState: {
    state: any;
    actions: any;
  };
  // Props để nhận followers data từ ApprovalInfoTab
  followersValue?: string[];
  onFollowersChange?: (selectedUserUuids: string[]) => void;
}

export interface BasicInfoTabRef {
  getFollowersValue: () => string[];
}

export const BasicInfoTab = forwardRef<BasicInfoTabRef, BasicInfoTabProps>(function BasicInfoTab(
  { formMode, formState, followersValue, onFollowersChange },
  ref
) {
  const selectedDocument = formState.state || {};

  const { users } = useUser();

  useImperativeHandle(
    ref,
    () => ({
      getFollowersValue: () => {
        const result = followersValue || [];
        console.log('BasicInfoTab - getFollowersValue called, result:', result);
        return result;
      }
    }),
    [followersValue]
  );

  // Convert users to options format for FormField
  // Use profile.uuid as value since backend expects UUID strings
  const userOptions = users.map(user => ({
    value: user.profile?.uuid || user.id.toString(),
    label: `${user.first_name || ''} ${user.last_name || ''}`.trim() || user.username || user.email
  }));

  return (
    <div className='border-b border-gray-200 bg-gray-50 p-4'>
      <div className='grid grid-cols-12 gap-8'>
        {/* Left Column - Employee Info */}
        <div className='col-span-6'>
          <div className='space-y-3'>
            {/* Nhân viên và Bộ phận cùng hàng */}
            <div className='grid grid-cols-1 gap-3'>
              <div className='flex items-center'>
                <Label className='w-20 text-sm text-gray-600'>Nhân viên</Label>
                <div className='flex flex-1 items-center'>
                  <div className='ml-2 flex-1'>
                    <FormField
                      name='ma_nvbh'
                      type='text'
                      disabled={true}
                      value={selectedDocument.ma_kh_data?.sales_rep_data?.ho_ten_nhan_vien || ''}
                      className='text-sm'
                    />
                  </div>
                </div>
              </div>

              <div className='flex items-center'>
                <Label className='w-20 text-sm text-gray-600'>Bộ phận</Label>
                <div className='flex-1'>
                  <FormField
                    name='ma_bp'
                    type='text'
                    disabled={true}
                    value={selectedDocument.ma_bp_data?.ten_bp || ''}
                    className='text-sm'
                  />
                </div>
              </div>
            </div>

            {/* Diễn giải */}
            <div className='flex items-center'>
              <Label className='w-20 text-sm text-gray-600'>Diễn giải</Label>
              <div className='flex-1'>
                <FormField
                  name='dien_giai'
                  type='text'
                  disabled={true}
                  value={selectedDocument.dien_giai || ''}
                  className='text-sm'
                />
              </div>
            </div>

            {/* Tiền */}
            <div className='flex items-center'>
              <Label className='w-20 text-sm text-gray-600'>Tiền</Label>
              <div className='flex-1'>
                <FormField
                  name='t_tien2'
                  type='text'
                  disabled={true}
                  value={selectedDocument.t_tien2 || '0.00'}
                  className='text-sm font-medium'
                />
              </div>
            </div>
          </div>
        </div>

        {/* Right Column - Document Info */}
        <div className='col-span-6'>
          <div className='space-y-3'>
            {/* Đơn từ */}
            <div className='flex items-center'>
              <Label className='w-20 text-sm text-gray-600'>Đơn từ</Label>
              <div className='flex items-center'>
                <AritoColoredDot color='red' />
                <span className='ml-1 text-sm font-medium'>
                  {selectedDocument.ma_gd === 'DH' ? 'Phiếu yêu cầu duyệt đơn hàng' : 'Phiếu yêu cầu duyệt đơn hàng'}
                </span>
              </div>
            </div>

            {/* Số đơn từ */}
            <div className='flex items-center'>
              <Label className='w-20 text-sm text-gray-600'>Số đơn từ</Label>
              <div className='flex-1'>
                <FormField
                  name='so_ct'
                  type='text'
                  disabled={true}
                  value={selectedDocument.so_ct || ''}
                  className='text-sm font-medium'
                />
              </div>
            </div>

            {/* Ngày lập đơn */}
            <div className='flex items-center'>
              <Label className='w-20 text-sm text-gray-600'>Ngày lập đơn</Label>
              <div className='flex-1'>
                <FormField
                  name='ngay_ct'
                  type='date'
                  disabled={true}
                  value={selectedDocument.ngay_ct || ''}
                  className='text-sm'
                />
              </div>
            </div>

            {/* Tiến trình */}
            <div className='flex items-center'>
              <Label className='w-20 text-sm text-gray-600'>Tiến trình</Label>
              <div className='flex-1'>
                <FormField
                  name='status'
                  type='select'
                  disabled={true}
                  value={selectedDocument.status || ''}
                  options={[
                    { value: '0', label: 'Nháp' },
                    { value: '1', label: 'Chờ duyệt' },
                    { value: '5', label: 'Đã duyệt' },
                    { value: '9', label: 'Đóng' }
                  ]}
                  className='text-sm'
                />
              </div>
            </div>

            {/* Theo dõi */}
            <div className='flex items-start'>
              <Label className='mt-2 w-20 text-sm text-gray-600'>Theo dõi</Label>
              <div className='flex-1 space-y-2'>
                {/* Hiển thị danh sách followers hiện tại */}
                {followersValue && followersValue.length > 0 && (
                  <div className='flex flex-wrap gap-1'>
                    {followersValue.map((followerUuid: string, index: number) => {
                      const user = users.find(
                        u => u.profile?.uuid === followerUuid || u.id.toString() === followerUuid
                      );
                      return (
                        <span key={index} className='rounded bg-purple-100 px-2 py-1 text-xs text-purple-800'>
                          {user
                            ? `${user.first_name || ''} ${user.last_name || ''}`.trim() || user.username || user.email
                            : followerUuid}
                        </span>
                      );
                    })}
                  </div>
                )}

                {/* FormField để chọn */}
                <FormField
                  name='followers'
                  type='multiselect'
                  value={followersValue || []}
                  options={userOptions}
                  disabled={formMode === 'view'}
                  className='text-sm'
                  placeholder='Chọn người theo dõi...'
                  onValueChange={(selectedIds: string[]) => onFollowersChange?.(selectedIds)}
                />
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
});

BasicInfoTab.displayName = 'BasicInfoTab';
