import { Printer, Plus, Pencil, Trash, Copy, LogOut, RefreshCw, Table, Save } from 'lucide-react';
import { useState } from 'react';
import { useWatch } from 'react-hook-form';
import { BasicInfoTab, BottomBar, DetailItemTab, ExchangeRateTab, OtherTab, PaymentInfoTab, BankFeeTab } from './tabs';
import { AritoForm, AritoHeaderTabs, AritoActionButton } from '@/components/custom/arito';
import { exportCreditAdviceSchema, initialFormValues } from '../../schemas';
import { useBankFeeRows, useDetailItemRows } from './tabs/hooks';
import { calculatePaymentTotals } from '../../utils/calc-utils';
import { useAuth } from '@/contexts/auth-context';
import { transformFormData } from '../../utils';
import { useFormFieldState } from '../../hooks';
import { HistoryTab } from './HistoryTab';
import { FormMode } from '@/types/form';

const DetailItemTabWrapper = ({
  formMode,
  detailRows,
  detailSelectedRowUuid,
  detailHandleRowClick,
  detailHandleAddRow,
  detailHandleDeleteRow,
  detailHandleCopyRow,
  detailHandlePasteRow,
  detailHandleMoveRow,
  detailHandleCellValueChange
}: any) => {
  const [ma_ngv, dien_giai] = useWatch({ name: ['ma_ngv', 'dien_giai'] });

  return (
    <DetailItemTab
      formMode={formMode}
      rows={detailRows}
      selectedRowUuid={detailSelectedRowUuid}
      onRowClick={detailHandleRowClick}
      onAddRow={detailHandleAddRow}
      onDeleteRow={detailHandleDeleteRow}
      onCopyRow={detailHandleCopyRow}
      onPasteRow={detailHandlePasteRow}
      onMoveRow={detailHandleMoveRow}
      onCellValueChange={detailHandleCellValueChange}
      ma_ngv={ma_ngv || '1'}
      dien_giai={dien_giai}
    />
  );
};

export const AddForm = ({
  formMode,
  initialData,
  onSubmit,
  onClose,
  currentIndex,
  totalCount
}: {
  formMode: FormMode;
  initialData: any;
  onSubmit: (data: any) => void;
  onClose: () => void;
  currentIndex?: number;
  totalCount?: number;
}) => {

  const [activeTab, setActiveTab] = useState('info');

  const {
    rows: detailRows,
    setRows: setDetailRows,
    selectedRowUuid: detailSelectedRowUuid,
    handleRowClick: detailHandleRowClick,
    handleAddRow: detailHandleAddRow,
    handleDeleteRow: detailHandleDeleteRow,
    handleCopyRow: detailHandleCopyRow,
    handlePasteRow: detailHandlePasteRow,
    handleMoveRow: detailHandleMoveRow,
    handleCellValueChange: detailHandleCellValueChange
  } = useDetailItemRows(initialData?.chi_tiet_data || []);
  const {
    rows: bankFeeRows,
    selectedRowUuid: bankFeeSelectedRowUuid,
    handleRowClick: bankFeeHandleRowClick,
    handleAddRow: bankFeeHandleAddRow,
    handleDeleteRow: bankFeeHandleDeleteRow,
    handleCopyRow: bankFeeHandleCopyRow,
    handlePasteRow: bankFeeHandlePasteRow,
    handleMoveRow: bankFeeHandleMoveRow,
    handleCellValueChange: bankFeeHandleCellValueChange
  } = useBankFeeRows(initialData?.chi_tiet_phi_data || []);
  const { state, actions } = useFormFieldState(initialData);
  const { entityUnit } = useAuth();

  
  const { tong_tien, tong_thanh_toan, tong_thue } = calculatePaymentTotals(detailRows, bankFeeRows);

  const handleSubmit = (data: any) => {
    const formData = transformFormData(data, state, detailRows, bankFeeRows, entityUnit);
    onSubmit(formData);
  };

  const getTitle = () => {
    if (formMode !== 'view') {
      return formMode === 'add' ? 'Mới' : 'Sửa';
    }

    switch (activeTab) {
      case 'history':
        return 'Lịch sử';
      default:
        return 'Giấy báo có';
    }
  };

  const getActionButtons = () => {
    if (formMode !== 'view') {
      return (
        <>
          <AritoActionButton title='Lưu' icon={Save} variant='secondary' type='submit' />
          <AritoActionButton title='Đóng' icon={LogOut} variant='destructive' onClick={onClose} />
        </>
      );
    }

    switch (activeTab) {
      case 'history':
        return (
          <>
            <AritoActionButton title='Refresh' icon={RefreshCw} variant='secondary' onClick={() => {}} />
            <AritoActionButton title='Cố định cột' icon={Table} variant='secondary' onClick={() => {}} />
            <AritoActionButton title='Đóng' icon={LogOut} variant='destructive' onClick={onClose} />
          </>
        );
      default:
        return (
          <>
            <AritoActionButton title='In' icon={Printer} variant='secondary' onClick={() => {}} />
            <AritoActionButton title='Thêm' icon={Plus} variant='primary' onClick={() => {}} />
            <AritoActionButton title='Sửa' icon={Pencil} variant='secondary' onClick={() => {}} />
            <AritoActionButton title='Xóa' icon={Trash} variant='destructive' onClick={() => {}} />
            <AritoActionButton title='Sao chép' icon={Copy} variant='secondary' onClick={() => {}} />
            <AritoActionButton title='Đóng' icon={LogOut} variant='destructive' onClick={onClose} />
          </>
        );
    }
  };

  if (initialData && formMode === 'add') {
    initialData = {
      ...initialData,
      ngay_ct: new Date().toISOString().split('T')[0],
      ngay_lct: new Date().toISOString().split('T')[0]
    };
  }

  return (
    <div className='h-full flex-1 overflow-auto'>
      <AritoForm
        mode={formMode}
        hasAritoActionBar={true}
        initialData={initialData || initialFormValues}
        onSubmit={handleSubmit}
        onClose={onClose}
        schema={exportCreditAdviceSchema}
        title={getTitle()}
        subTitle='Giấy báo có'
        headerFields={
          <AritoHeaderTabs
            defaultTabIndex={0}
            onTabChange={tabId => setActiveTab(tabId)}
            tabs={[
              {
                id: 'info',
                label: 'Thông tin',
                component: <BasicInfoTab formMode={formMode} formState={{ state, actions }} />
              },
              ...(formMode !== 'add'
                ? [{ id: 'history', label: 'Lịch sử', component: <HistoryTab formMode={formMode} /> }]
                : [])
            ]}
          />
        }
        tabs={
          activeTab === 'info'
            ? [
                {
                  id: 'details',
                  label: 'Chi tiết',
                  component: (
                    <DetailItemTabWrapper
                      formMode={formMode}
                      detailRows={detailRows}
                      detailSelectedRowUuid={detailSelectedRowUuid}
                      detailHandleRowClick={detailHandleRowClick}
                      detailHandleAddRow={detailHandleAddRow}
                      detailHandleDeleteRow={detailHandleDeleteRow}
                      detailHandleCopyRow={detailHandleCopyRow}
                      detailHandlePasteRow={detailHandlePasteRow}
                      detailHandleMoveRow={detailHandleMoveRow}
                      detailHandleCellValueChange={detailHandleCellValueChange}
                    />
                  )
                },
                {
                  id: 'paymentInfo',
                  label: 'Thông tin thanh toán',
                  component: <PaymentInfoTab formMode={formMode} formState={{ state, actions }} />
                },
                {
                  id: 'exchangeRate',
                  label: 'Tỷ giá',
                  component: <ExchangeRateTab formMode={formMode} />
                },
                {
                  id: 'bankFee',
                  label: 'Phí ngân hàng',
                  component: (
                    <BankFeeTab
                      formMode={formMode}
                      rows={bankFeeRows}
                      selectedRowUuid={bankFeeSelectedRowUuid}
                      onRowClick={bankFeeHandleRowClick}
                      onAddRow={bankFeeHandleAddRow}
                      onDeleteRow={bankFeeHandleDeleteRow}
                      onCopyRow={bankFeeHandleCopyRow}
                      onPasteRow={bankFeeHandlePasteRow}
                      onMoveRow={bankFeeHandleMoveRow}
                      onCellValueChange={bankFeeHandleCellValueChange}
                    />
                  )
                },
                {
                  id: 'other',
                  label: 'Khác',
                  component: <OtherTab formMode={formMode} />
                }
              ]
            : []
        }
        actionButtons={getActionButtons()}
        bottomBar={
          <BottomBar tong_tien={tong_tien} tong_thanh_toan={tong_thanh_toan} tong_thue={tong_thue} state={state} />
        }
      />
    </div>
  );
};
