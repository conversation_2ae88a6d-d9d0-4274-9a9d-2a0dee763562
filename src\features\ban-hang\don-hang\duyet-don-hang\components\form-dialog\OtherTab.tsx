import {
  QUERY_KEYS,
  diaChiSearchColumns,
  phuongThucGiaoHangSearchColumns,
  phuongThucVanChuyenSearchColumns,
  phuongThucThanhToanSearchColumns
} from '@/constants';
import { <PERSON><PERSON><PERSON><PERSON>, Phuong<PERSON>huc<PERSON>iao<PERSON><PERSON>, PhuongTienVan<PERSON>huyen, PhuongThucThanhToan } from '@/types/schemas';
import { FileSelectField } from '@/components/custom/arito/form/search-fields';
import { FormField, SearchField } from '@/components/custom/arito';
import { FormFieldState, FormFieldActions } from '../../hooks';
import { Label } from '@/components/ui/label';
import { FormMode } from '@/types/form';

interface OtherTabProps {
  formMode: FormMode;
  formState: {
    state: FormFieldState;
    actions: FormFieldActions;
  };
}

export function OtherTab({ formMode, formState: { state, actions } }: OtherTabProps) {
  return (
    <div className='p-4'>
      <div className='flex items-center'>
        <Label className='w-40'>Đ<PERSON>a chỉ giao hàng</Label>
        <SearchField<DiaChi>
          type='text'
          value={state.diaChi?.ma_dc || ''}
          relatedFieldValue={state.diaChi?.ten_dc || ''}
          searchEndpoint={`/${QUERY_KEYS.DIA_CHI}/`}
          searchColumns={diaChiSearchColumns}
          columnDisplay='ma_dc'
          displayRelatedField='ten_dc'
          dialogTitle='Danh mục địa chỉ giao hàng'
          onRowSelection={actions.setDiaChi}
          disabled={formMode === 'view'}
        />
      </div>
      <div className='flex items-center'>
        <Label className='w-40'>Phương tiện v/c</Label>
        <SearchField<PhuongTienVanChuyen>
          type='text'
          value={state.phuongTienVanChuyen?.ma_ptvc || ''}
          relatedFieldValue={state.phuongTienVanChuyen?.ten_ptvc || ''}
          searchEndpoint={`/${QUERY_KEYS.PHUONG_TIEN_VAN_CHUYEN}/`}
          searchColumns={phuongThucVanChuyenSearchColumns}
          columnDisplay='ma_ptvc'
          displayRelatedField='ten_ptvc'
          dialogTitle='Danh mục phương tiện vận chuyển'
          onRowSelection={actions.setPhuongTienVanChuyen}
          disabled={formMode === 'view'}
        />
      </div>
      <div className='flex items-center'>
        <Label className='w-40'>Ph/th thanh toán</Label>
        <SearchField<PhuongThucThanhToan>
          type='text'
          value={state.phuongThucThanhToan?.ma_pttt || ''}
          relatedFieldValue={state.phuongThucThanhToan?.ten_pttt || ''}
          searchEndpoint={`/${QUERY_KEYS.PHUONG_THUC_THANH_TOAN}/`}
          searchColumns={phuongThucThanhToanSearchColumns}
          columnDisplay='ma_pttt'
          displayRelatedField='ten_pttt'
          dialogTitle='Danh mục phương thức thanh toán'
          onRowSelection={actions.setPhuongThucThanhToan}
          disabled={formMode === 'view'}
        />
      </div>
      <div className='flex items-center'>
        <Label className='w-40'>Ph/th giao hàng</Label>
        <SearchField<PhuongThucGiaoHang>
          type='text'
          value={state.phuongTienGiaoHang?.ma_ptgh || ''}
          relatedFieldValue={state.phuongTienGiaoHang?.ten_ptgh || ''}
          searchEndpoint={`/${QUERY_KEYS.PHUONG_THUC_GIAO_HANG}/`}
          searchColumns={phuongThucGiaoHangSearchColumns}
          columnDisplay='ma_ptgh'
          displayRelatedField='ten_ptgh'
          dialogTitle='Danh mục phương thức giao hàng'
          onRowSelection={actions.setPhuongTienGiaoHang}
          disabled={formMode === 'view'}
        />
      </div>
      <div className='flex items-center'>
        <Label className='w-40'>Giao dịch</Label>
        <FormField
          name='ma_gd'
          className='w-[200px]'
          type='select'
          disabled={formMode === 'view'}
          options={[
            { value: 'DH', label: 'DH. Đơn hàng' },
            { value: 'HD', label: 'HD. Hợp đồng' }
          ]}
        />
      </div>
      <div className='flex items-center'>
        <Label className='w-40'>Treo đơn hàng</Label>
        <div className='w-full'>
          <FormField name='treo_dh' type='text' disabled={true} placeholder='Không treo' />
        </div>
      </div>
      <div className='flex items-center'>
        <FileSelectField
          label='Chọn file'
          formMode={formMode}
          labelClassName='w-32'
          onFileChange={file => console.log(file)}
        />
      </div>
    </div>
  );
}
