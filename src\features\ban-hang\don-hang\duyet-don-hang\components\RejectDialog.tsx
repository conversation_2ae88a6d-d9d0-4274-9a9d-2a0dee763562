import React, { useState } from 'react';
import { Button } from '@mui/material';
import { AritoDialog, AritoIcon } from '@/components/custom/arito';
import { Label } from '@/components/ui/label';

interface RejectDialogProps {
  open: boolean;
  onClose: () => void;
  onConfirm: (action: 'cancel' | 'reject', reason?: string) => void;
  title?: string;
}

const RejectDialog = ({ open, onClose, onConfirm, title = 'Hủy/từ chối duyệt đơn từ' }: RejectDialogProps) => {
  const [selectedAction, setSelectedAction] = useState<'cancel' | 'reject'>('reject');
  const [reason, setReason] = useState('');

  const handleConfirm = () => {
    onConfirm(selectedAction, reason);
    onClose();
  };

  const handleClose = () => {
    // Reset form when closing
    setSelectedAction('reject');
    setReason('');
    onClose();
  };

  return (
    <AritoDialog
      open={open}
      onClose={handleClose}
      title={title}
      maxWidth='lg'
      disableBackdropClose={false}
      disableEscapeKeyDown={true}
      showFullscreenToggle={false}
      titleIcon={<AritoIcon icon={260} />}
      actions={
        <>
          <Button
            onClick={handleConfirm}
            type='submit'
            variant='contained'
            className='bg-[rgba(15,118,110,0.9)] normal-case hover:bg-[rgba(15,118,110,1)]'
          >
            <AritoIcon icon={884} className='mr-2' />
            Đồng ý
          </Button>
          <Button onClick={handleClose} variant='outlined'>
            <AritoIcon icon={885} className='mr-2' />
            Hủy
          </Button>
        </>
      }
    >
      <div className='min-w-[700px] p-4'>
        {/* Xử lý section */}
        <div className='mb-4'>
          <div className='flex items-center gap-6'>
            <Label className='min-w-[60px] text-sm font-medium'>Xử lý</Label>
            <div className='flex items-center gap-6'>
              <label className='flex items-center'>
                <input
                  type='radio'
                  name='action'
                  value='cancel'
                  checked={selectedAction === 'cancel'}
                  onChange={e => setSelectedAction(e.target.value as 'cancel')}
                  className='mr-2'
                />
                <span className='text-sm'>Hủy (Hủy bỏ đơn từ)</span>
              </label>
              <label className='flex items-center'>
                <input
                  type='radio'
                  name='action'
                  value='reject'
                  checked={selectedAction === 'reject'}
                  onChange={e => setSelectedAction(e.target.value as 'reject')}
                  className='mr-2'
                />
                <span className='text-sm'>Từ chối (không đồng ý, xem lại lý do và thực hiện)</span>
              </label>
            </div>
          </div>
        </div>

        {/* Lý do section */}
        <div className='mb-4'>
          <div className='flex items-start'>
            <Label className='mt-2 min-w-[60px] text-sm font-medium'>Lý do</Label>
            <div className='ml-6 flex-1'>
              <textarea
                value={reason}
                onChange={e => setReason(e.target.value)}
                placeholder='Lý do hủy/từ chối phê duyệt đơn từ'
                className='min-h-[80px] w-full resize-none rounded border border-gray-300 p-2 text-sm focus:border-blue-500 focus:outline-none'
                rows={3}
              />
            </div>
          </div>
        </div>
      </div>
    </AritoDialog>
  );
};

export default RejectDialog;
