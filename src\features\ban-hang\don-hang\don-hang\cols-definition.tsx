import { GridColDef } from '@mui/x-data-grid';
import { format } from 'date-fns';
import { Checkbox } from '@/components/ui/checkbox';

export const getDataTableColumns = (handleViewClick: () => void): GridColDef[] => [
  {
    field: 'status',
    headerName: 'Trạng thái',
    width: 120,
    renderCell: params => {
      const status = params.row.status;
      switch (status) {
        case '0':
          return 'Lập chứng từ';
        case '1':
          return 'Chờ duyệt';
        case '5':
          return 'Xuất hóa đơn';
        case '6':
          return 'Đã xuất hóa đơn điện tử';
        case '7':
          return 'Bỏ duyệt đơn hàng';
        default:
          return '';
      }
    }
  },
  {
    field: 'so_ct',
    headerName: 'Số c/từ',
    width: 120,
    renderCell: params => (
      <button onClick={handleViewClick} className='hover:text-blue-500 hover:underline'>
        {params.row.so_ct}
      </button>
    )
  },
  {
    field: 'ngay_ct',
    headerName: 'Ngày c/từ',
    width: 120,
    renderCell: params => format(params.row.ngay_ct, 'dd/MM/yyyy')
  },
  {
    field: 'ma_kh',
    headerName: 'Mã khách hàng',
    width: 120,
    renderCell: params => params.row.ma_kh_data?.customer_code
  },
  {
    field: 'ten_kh',
    headerName: 'Tên khách hàng',
    width: 200,
    renderCell: params => params.row.ma_kh_data?.customer_name
  },
  {
    field: 'dien_giai',
    headerName: 'Diễn giải',
    width: 200,
    renderCell: params => params.row.dien_giai
  },
  {
    field: 't_tt_nt',
    headerName: 'Tổng tiền',
    width: 150
  },
  {
    field: 'ma_nt',
    headerName: 'Ngoại tệ',
    width: 120,
    renderCell: params => params.row.ma_nt_data?.ma_nt
  },
  {
    field: 'ma_ngv',
    headerName: 'Loại chứng từ',
    width: 120,
    renderCell: params => {
      switch (params.row.ma_ngv) {
        case '5':
          return 'Đơn hàng';
        case '4':
          return 'Hợp đồng';
      }
    }
  }
];

export const getInputTableColumns = (): GridColDef[] => [
  {
    field: 'ma_vt',
    headerName: 'Mã sản phẩm',
    width: 120,
    renderCell: params => params.row.ma_vt_data?.ma_vt
  },
  {
    field: 'ten_vt',
    headerName: 'Tên sản phẩm',
    width: 200,
    renderCell: params => params.row.ma_vt_data?.ten_vt
  },
  {
    field: 'dvt',
    headerName: 'Đvt',
    width: 100,
    renderCell: params => params.row.dvt_data?.dvt
  },
  {
    field: 'ma_kho',
    headerName: 'Mã kho',
    width: 120,
    renderCell: params => params.row.ma_kho_data?.ma_kho
  },
  {
    field: 'so_luong',
    headerName: 'Số lượng',
    width: 100
  },
  {
    field: 'ct_km',
    headerName: 'Loại hàng',
    width: 120,
    renderCell: params => {
      const ctKm = params.row.ct_km;
      switch (ctKm) {
        case false:
          return 'Hàng bán';
        case true:
          return 'Hàng KM';
      }
    }
  },
  {
    field: 'ma_loai_gb',
    headerName: 'Loại giá',
    width: 150
  },
  {
    field: 'gia_nt1',
    headerName: 'Giá chuẩn %',
    width: 150
  },
  {
    field: 'gia_nt2',
    headerName: 'Giá bán %',
    width: 150
  },
  {
    field: 'tien_nt2',
    headerName: 'Thành tiền %',
    width: 150
  },
  {
    field: 'ngay_giao',
    headerName: 'Ngày giao',
    width: 150
  },
  {
    field: 'tl_ck',
    headerName: 'Tl ck(%)',
    width: 150
  },
  {
    field: 'ck_nt',
    headerName: 'Ch.khấu %s',
    width: 150
  },
  {
    field: 'ma_thue',
    headerName: 'Thuế suất',
    width: 100
  },
  {
    field: 'thue_nt',
    headerName: 'Thuế %',
    width: 100
  },
  {
    field: 'ma_bp',
    headerName: 'Bộ phận',
    width: 120,
    renderCell: params => params.row.ma_bp_data?.ma_bp
  },
  {
    field: 'ma_vv',
    headerName: 'Vụ việc',
    width: 120
  },
  {
    field: 'ma_hd',
    headerName: 'Hợp đồng',
    width: 120
  },
  {
    field: 'ma_dtt',
    headerName: 'Đợt thanh toán',
    width: 120
  },
  {
    field: 'ma_ku',
    headerName: 'Khế ước',
    width: 120
  },
  {
    field: 'ma_phi',
    headerName: 'Phí',
    width: 120
  },
  {
    field: 'ma_sp',
    headerName: 'Sản phẩm',
    width: 120,
    renderCell: params => params.row.ma_sp_data?.ma_vt
  },
  {
    field: 'ma_cp0',
    headerName: 'C/p không h/lệ',
    width: 120
  },
  {
    field: 'gia1',
    headerName: 'Giá chuẩn',
    width: 120
  },
  {
    field: 'gia2',
    headerName: 'Giá bán',
    width: 120
  },
  {
    field: 'tien2',
    headerName: 'Thành tiền',
    width: 120
  },
  {
    field: 'ck',
    headerName: 'Chiết khấu',
    width: 120
  },
  {
    field: 'thue',
    headerName: 'Thuế',
    width: 120
  },
  {
    field: 'sl_don_hang',
    headerName: 'Sl đơn hàng',
    width: 120
  },
  {
    field: 'sl_hd',
    headerName: 'Sl hoá đơn',
    width: 120
  },
  {
    field: 'sl_px',
    headerName: 'Sl xuất',
    width: 120
  },
  {
    field: 'so_ct_hd',
    headerName: 'Số hợp đồng',
    width: 120
  },
  {
    field: 'line_hd',
    headerName: 'Dòng HĐ',
    width: 120
  },
  {
    field: 'so_ct_bg',
    headerName: 'Số báo giá',
    width: 120
  },
  {
    field: 'line_bg',
    headerName: 'Dòng BG',
    width: 120
  }
];
