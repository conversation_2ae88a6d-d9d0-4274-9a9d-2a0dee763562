import { GridColDef } from '@mui/x-data-grid';
import React, { useEffect } from 'react';
import { InputTable } from '@/components/custom/arito';
import { useApprovalHistory } from '../../hooks';
import { FormMode } from '@/types/form';

interface ApprovalHistoryTabProps {
  formMode: FormMode;
  documentId?: string;
  formState?: {
    state: any;
    actions: any;
  };
}

interface ApprovalHistoryRecord {
  id: string;
  nguoi_thuc_hien: string;
  ten_nguoi_thuc_hien: string;
  hanh_dong: string;
  thoi_gian: string;
  ghi_chu?: string;
}

// Helper function to format action display text
const formatActionDisplay = (action: string, xprocess: string | null, cnote: string | null): string => {
  switch (action) {
    case 'submit':
      return 'Gửi duyệt';
    case 'approve':
      return 'Duyệt';
    case 'unapprove':
      return 'Bỏ duyệt';
    case 'refuse':
      return 'Từ chối';
    case 'bypass':
      return 'Bỏ qua duyệt (Admin)';
    default:
      return xprocess || action || 'Không xác định';
  }
};

// Helper function to format date display
const formatDateDisplay = (dateString: string): string => {
  try {
    const date = new Date(dateString);
    return date.toLocaleString('vi-VN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit'
    });
  } catch {
    return dateString;
  }
};

// Helper function to transform API data to display format
const transformApprovalHistoryData = (apiData: any[]): ApprovalHistoryRecord[] => {
  console.log('transformApprovalHistoryData: Input apiData:', apiData);

  if (!Array.isArray(apiData)) {
    console.warn('transformApprovalHistoryData: apiData is not an array:', apiData);
    return [];
  }

  const result = apiData.map(item => {
    const transformed = {
      id: item.id,
      nguoi_thuc_hien: item.history_user || 'N/A',
      ten_nguoi_thuc_hien: item.history_user || 'N/A',
      hanh_dong: formatActionDisplay(item.action, item.xprocess, item.cnote),
      thoi_gian: formatDateDisplay(item.history_date),
      ghi_chu: item.cnote
    };
    console.log('transformApprovalHistoryData: Transformed item:', { original: item, transformed });
    return transformed;
  });

  console.log('transformApprovalHistoryData: Final result:', result);
  return result;
};

// Định nghĩa cột cho InputTable
const getApprovalHistoryColumns = (): GridColDef[] => [
  {
    field: 'nguoi_thuc_hien',
    headerName: 'Người thực hiện',
    width: 150,
    renderCell: params => <span className='py-2 text-sm text-gray-900'>{params.value}</span>
  },
  {
    field: 'hanh_dong',
    headerName: 'Hành động',
    width: 150,
    renderCell: params => {
      const value = params.value as string;
      let textColor = 'text-gray-900';

      // Màu sắc theo loại hành động
      if (value.includes('Duyệt') || value.includes('Gửi duyệt')) {
        textColor = 'text-green-600';
      } else if (value.includes('Bỏ duyệt') || value.includes('Từ chối')) {
        textColor = 'text-red-600';
      } else if (value.includes('Admin')) {
        textColor = 'text-blue-600';
      }

      return <span className={`py-2 text-sm font-medium ${textColor}`}>{value}</span>;
    }
  },
  {
    field: 'thoi_gian',
    headerName: 'Thời gian',
    width: 180,
    renderCell: params => <span className='py-2 text-sm text-gray-900'>{params.value}</span>
  },
  {
    field: 'ghi_chu',
    headerName: 'Ghi chú',
    width: 250,
    renderCell: params => (
      <span className='py-2 text-sm text-gray-700' title={params.value}>
        {params.value || '-'}
      </span>
    )
  }
];

export const ApprovalHistoryTab: React.FC<ApprovalHistoryTabProps> = ({ documentId }) => {
  const { approvalHistory, isLoading, error, fetchApprovalHistory } = useApprovalHistory();

  // Fetch approval history when documentId changes
  useEffect(() => {
    if (documentId) {
      console.log('ApprovalHistoryTab: Fetching history for documentId:', documentId);
      fetchApprovalHistory(documentId);
    }
  }, [documentId, fetchApprovalHistory]);

  // Transform API data to display format
  const displayData = transformApprovalHistoryData(approvalHistory);

  // Debug logging
  console.log('ApprovalHistoryTab: approvalHistory:', approvalHistory);
  console.log('ApprovalHistoryTab: displayData:', displayData);
  console.log('ApprovalHistoryTab: isLoading:', isLoading);
  console.log('ApprovalHistoryTab: error:', error);

  // Log when displayData changes
  useEffect(() => {
    console.log('ApprovalHistoryTab: displayData changed:', displayData);
  }, [displayData]);

  if (error) {
    return (
      <div className='p-4'>
        <div className='text-sm text-red-600'>Lỗi khi tải lịch sử duyệt: {error}</div>
      </div>
    );
  }

  return (
    <div className='p-4'>
      <div style={{ width: '100%', maxWidth: '730px' }}>
        {isLoading ? (
          <div className='py-4 text-center'>
            <span className='text-gray-500'>Đang tải lịch sử duyệt...</span>
          </div>
        ) : displayData.length === 0 ? (
          <div className='py-4 text-center'>
            <span className='text-gray-500'>Chưa có lịch sử duyệt</span>
          </div>
        ) : (
          <>
            <div className='mb-2 text-xs text-gray-500'>Debug: Hiển thị {displayData.length} records</div>
            <InputTable
              rows={displayData}
              columns={getApprovalHistoryColumns()}
              getRowId={row => {
                console.log('InputTable getRowId called with row:', row);
                return row.id;
              }}
              onRowClick={() => {}} // Không cần xử lý click
            />
          </>
        )}
      </div>
    </div>
  );
};
