/**
 * Calculate totals from detail rows for PhieuThu (Receipt Voucher)
 * Uses actual field names from PhieuThu DetailTab
 */
export const calculateTotals = (detailRows: any[] = []) => {
  const t_tien_nt = detailRows.reduce(
    (sum: number, row: any) => sum + (row.tien_nt || row.id_hd_data?.tien_hd_nt || 0),
    0
  );

  return {
    t_tien_nt,
    t_tien: t_tien_nt
  };
};

/**
 * Calculate individual row amounts based on exchange rate
 * @param tien_nt - Amount in foreign currency
 * @param ty_gia - Exchange rate
 * @returns Calculated amount in VND
 */
export const calculateTienFromTienNt = (tien_nt: number | string, ty_gia: number | string): number => {
  const tienNt = Number(tien_nt) || 0;
  const tyGia = Number(ty_gia) || 1;

  return Math.round(tienNt * tyGia * 100) / 100;
};

/**
 * Calculate foreign currency amount from VND amount
 * @param tien - Amount in VND
 * @param ty_gia - Exchange rate
 * @returns Calculated amount in foreign currency
 */
export const calculateTienNtFromTien = (tien: number | string, ty_gia: number | string): number => {
  const tienVnd = Number(tien) || 0;
  const tyGia = Number(ty_gia) || 1;

  if (tyGia === 0) return 0;

  return Math.round((tienVnd / tyGia) * 100) / 100;
};

/**
 * Handle field updates for PhieuThu detail rows
 * Automatically calculates related fields when certain fields change
 */
export const handleUpdateRowFields = (row: any, field: string, newValue: any, ty_gia: number = 1) => {
  const updates: any = {};

  if (field === 'tien_nt') {
    // When foreign currency amount changes, calculate VND amount
    updates.tien = calculateTienFromTienNt(newValue, ty_gia);
  } else if (field === 'tien') {
    // When VND amount changes, calculate foreign currency amount
    updates.tien_nt = calculateTienNtFromTien(newValue, ty_gia);
  }

  return updates;
};

/**
 * Example usage and test cases for the calculation functions
 *
 * Test the exchange rate calculations:
 * - calculateTienFromTienNt(100, 24000) should return 2400000 (100 USD * 24000 VND/USD)
 * - calculateTienNtFromTien(2400000, 24000) should return 100 (2400000 VND / 24000 VND/USD)
 * - handleUpdateRowFields(row, 'tien_nt', 100, 24000) should return { tien: 2400000 }
 */
