import { FormMode } from '@/types/form';

/**
 * Get form title based on form mode for PhieuThu
 * @param formMode - Current form mode
 * @returns Appropriate title string
 */
export const getFormTitle = (formMode: FormMode): string => {
  switch (formMode) {
    case 'add':
      return 'Mới';
    case 'edit':
      return 'Chỉnh sửa';
    case 'view':
      return 'Xem';
    default:
      return 'Phiếu thu tiền';
  }
};

/**
 * Get form action buttons configuration based on form mode
 * @param formMode - Current form mode
 * @returns Button configuration object
 */
export const getFormActionButtons = (formMode: FormMode) => {
  const baseButtons = {
    showSave: formMode !== 'view',
    showCancel: true,
    showClose: formMode === 'view'
  };

  switch (formMode) {
    case 'add':
      return {
        ...baseButtons,
        saveText: 'Lưu',
        cancelText: 'Hủy'
      };
    case 'edit':
      return {
        ...baseButtons,
        saveText: 'Cập nhật',
        cancelText: 'Hủy'
      };
    case 'view':
      return {
        ...baseButtons,
        closeText: 'Đóng'
      };
    default:
      return baseButtons;
  }
};

/**
 * Get form subtitle for PhieuThu
 * @param currentIndex - Current receipt index (0-based)
 * @param totalCount - Total number of receipts
 * @returns Subtitle string
 */
export const getFormSubTitle = (currentIndex?: number, totalCount?: number): string => {
  if (currentIndex !== undefined && totalCount !== undefined && totalCount > 1) {
    return `Phiếu thu tiền (${currentIndex + 1}/${totalCount})`;
  }
  return 'Phiếu thu tiền';
};

/**
 * Check if form is in read-only mode
 * @param formMode - Current form mode
 * @returns Boolean indicating if form should be read-only
 */
export const isFormReadOnly = (formMode: FormMode): boolean => {
  return formMode === 'view';
};

/**
 * Get form validation rules based on form mode
 * @param formMode - Current form mode
 * @returns Validation configuration
 */
export const getFormValidationRules = (formMode: FormMode) => {
  const isReadOnly = isFormReadOnly(formMode);

  return {
    required: !isReadOnly,
    disabled: isReadOnly,
    validateOnChange: !isReadOnly,
    validateOnBlur: !isReadOnly
  };
};
