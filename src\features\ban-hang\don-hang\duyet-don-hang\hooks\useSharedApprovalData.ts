import { useState, useCallback } from 'react';

interface ApprovalData {
  approve: Array<{
    users: string[];
    watchers: string[];
  }>;
  followers: string[];
}

interface UseSharedApprovalDataReturn {
  approvalData: ApprovalData;
  updateApprovalLevels: (levels: any[]) => void;
  updateFollowers: (followers: string[]) => void;
  getApiData: () => ApprovalData;
  resetData: () => void;
}

/**
 * Hook để quản lý dữ liệu approval chung giữa các tabs
 */
export const useSharedApprovalData = (initialData?: any): UseSharedApprovalDataReturn => {
  const [approvalData, setApprovalData] = useState<ApprovalData>(() => {
    const initial = initialData?.approval_level_data;
    return {
      approve: initial?.approve || [],
      followers: initial?.followers || []
    };
  });

  const updateApprovalLevels = useCallback((levels: any[]) => {
    const approve = levels.map(level => ({
      users: level.users?.map((user: any) => user.uuid) || [],
      watchers: level.watchers?.map((watcher: any) => watcher.uuid) || []
    }));

    setApprovalData(prev => ({
      ...prev,
      approve
    }));
  }, []);

  const updateFollowers = useCallback((followers: string[]) => {
    setApprovalData(prev => ({
      ...prev,
      followers
    }));
  }, []);

  const getApiData = useCallback(() => {
    return approvalData;
  }, [approvalData]);

  const resetData = useCallback(() => {
    const initial = initialData?.approval_level_data;
    setApprovalData({
      approve: initial?.approve || [],
      followers: initial?.followers || []
    });
  }, [initialData]);

  return {
    approvalData,
    updateApprovalLevels,
    updateFollowers,
    getApiData,
    resetData
  };
};
