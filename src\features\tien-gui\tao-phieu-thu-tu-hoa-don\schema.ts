import { z } from 'zod';

/**
 * Schema for the "Tạo phiếu thu từ hóa đơn" form
 */
export const createReceiptFromInvoiceSchema = z.object({
  xu_ly: z.enum(['1', '2'], { required_error: '<PERSON><PERSON> lý không được để trống' }),
  ngay_ct1: z.string().min(1, '<PERSON><PERSON><PERSON> chứng từ từ không được để trống'),
  ngay_ct2: z.string().min(1, '<PERSON><PERSON><PERSON> chứng từ đến không được để trống'),
  so_ct1: z.coerce.number().optional().nullable(),
  so_ct2: z.coerce.number().optional().nullable(),
  ma_ct: z.string().optional().nullable(),
  ma_kh: z.string().optional().nullable(),
  ma_nt: z.string().optional().nullable(),
  ma_nk: z.string().optional().nullable(),
  unit_id: z.string().optional().nullable(),
  user_id0: z.string().min(1, 'User ID không được để trống'),
  status: z.number().default(1),
  ct: z.string().optional().nullable()
});

/**
 * Type definition for the form values
 */
export type CreateReceiptFromInvoiceFormValues = z.infer<typeof createReceiptFromInvoiceSchema>;

/**
 * Initial values for the form
 */
export const initialValues: CreateReceiptFromInvoiceFormValues = {
  xu_ly: '1',
  ngay_ct1: new Date(new Date().getFullYear(), new Date().getMonth() - 1, new Date().getDate() + 1)
    .toISOString()
    .split('T')[0],
  ngay_ct2: new Date().toISOString().split('T')[0],
  so_ct1: 1,
  so_ct2: 50,
  ma_ct: null,
  ma_kh: null,
  ma_nt: null,
  ma_nk: null,
  unit_id: '', // Default unit_id từ curl command
  user_id0: '0',
  status: 1,
  ct: null
};

/**
 * Schema for the "Tạo phiếu thu" form
 */
export const createReceiptSchema = z.object({
  // Chứng từ
  ma_ct: z.string().optional().nullable(),

  // Ngày chứng từ
  ngay_ct: z.string().min(1, 'Ngày chứng từ không được để trống'),

  // Quyển/Số chứng từ
  ma_nk: z.string().optional().nullable(),

  // Ngân hàng
  tknh: z.string().optional().nullable(),

  // Tài khoản nợ
  tk: z.string().min(1, 'Tài khoản nợ không được để trống'),

  // Loại phiếu thu
  loai_pt: z.string().min(1, 'Loại phiếu thu không được để trống'),

  // Đơn vị
  unit_id: z.string().optional().nullable(),

  // Dùng ngày hóa đơn làm ngày phiếu thu
  ngay_hd_yn: z.boolean().default(true),

  // Xem chứng từ đã tạo
  view_rs: z.boolean().default(false)
});

/**
 * Type definition for the create receipt form values
 */
export type CreateReceiptFormValues = z.infer<typeof createReceiptSchema>;

/**
 * Initial values for the create receipt form
 */
export const initialCreateReceiptValues: CreateReceiptFormValues = {
  ma_ct: '',
  ngay_ct: new Date().toISOString().split('T')[0],
  ma_nk: '',
  tknh: '',
  tk: '111', // Tài khoản tiền mặt mặc định
  loai_pt: '1',
  unit_id: null,
  ngay_hd_yn: true,
  view_rs: false
};
