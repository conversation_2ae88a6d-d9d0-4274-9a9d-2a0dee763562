import { useState } from 'react';
import { KhachHang, AccountModel, Quyen<PERSON>hungTu, Han<PERSON><PERSON><PERSON><PERSON><PERSON>, DonViCoSo } from '@/types/schemas';

export interface FormFieldState {
  khachHang: KhachHang | null;
  account: AccountModel | null;
  quyenChungTu: QuyenChungTu | null;
  hanThanhToan: HanThanhToan | null;
  donViCoSo: DonViCoSo | null;
  soChungTu: string;
}

export interface FormFieldActions {
  setKhachHang: (khachHang: KhachHang) => void;
  setAccount: (account: AccountModel) => void;
  setQuyenChungTu: (quyenChungTu: QuyenChungTu) => void;
  setHanThanhToan: (hanThanhToan: HanThanhToan) => void;
  setDonViCoSo: (donViCoSo: DonViCoSo) => void;
  setSoChungTu: (soChungTu: string) => void;

  // Utility functions
  resetState: () => void;
  updateState: (updates: Partial<FormFieldState>) => void;
}

export interface UseFormFieldStateReturn {
  state: FormFieldState;
  actions: FormFieldActions;
}

const initialState: FormFieldState = {
  khachHang: null,
  account: null,
  quyenChungTu: null,
  hanThanhToan: null,
  donViCoSo: null,
  soChungTu: ''
};

function transformInitialData(initialData?: any): Partial<FormFieldState> {
  if (!initialData) return {};

  return {
    khachHang: initialData.ma_kh_data || null,
    account: initialData.tk_data || null,
    quyenChungTu: initialData.ma_nk_data || null,
    hanThanhToan: initialData.ma_tt_data || null,
    donViCoSo: initialData.unit_id_data || null,
    soChungTu: initialData.so_ct || ''
  };
}

export function useFormFieldState(initialData?: any): UseFormFieldStateReturn {
  const [state, setState] = useState<FormFieldState>({
    ...initialState,
    ...transformInitialData(initialData)
  });

  const actions: FormFieldActions = {
    // Search field setters
    setKhachHang: (khachHang: KhachHang) => {
      setState(prev => ({
        ...prev,
        khachHang
      }));
    },

    setAccount: (account: AccountModel) => {
      setState(prev => ({
        ...prev,
        account
      }));
    },

    setQuyenChungTu: (quyenChungTu: QuyenChungTu) => {
      setState(prev => ({
        ...prev,
        quyenChungTu
      }));
    },

    setHanThanhToan: (hanThanhToan: HanThanhToan) => {
      setState(prev => ({
        ...prev,
        hanThanhToan
      }));
    },

    setDonViCoSo: (donViCoSo: DonViCoSo) => {
      setState(prev => ({
        ...prev,
        donViCoSo
      }));
    },

    setSoChungTu: (soChungTu: string) => {
      setState(prev => ({
        ...prev,
        soChungTu
      }));
    },

    resetState: () => {
      setState(initialState);
    },

    updateState: (updates: Partial<FormFieldState>) => {
      setState(prev => ({ ...prev, ...updates }));
    }
  };

  return { state, actions };
}
