/**
 * Calculate totals from detail rows based on DetailTab columns
 */
export const calculateTotals = (detailRows: any[] = []) => {
  let t_so_luong = 0;
  let t_tien_nt2 = 0;
  let t_thue_nt = 0;
  let t_ck_nt = 0;
  let t_km_nt = 0;

  detailRows.forEach(row => {
    const soLuong = Number(row.so_luong) || 0;
    const tienNt2 = Number(row.tien_nt2) || 0;
    const thueNt = Number(row.thue_nt) || 0;
    const ckNt = Number(row.ck_nt) || 0;
    const ctKm = row.ct_km || '0';

    t_so_luong += soLuong;
    t_thue_nt += thueNt;
    t_ck_nt += ckNt;

    if (ctKm === '1') {
      t_km_nt += tienNt2;
    } else {
      t_tien_nt2 += tienNt2;
    }
  });

  const t_tc_tien_nt2 = t_tien_nt2;
  const t_tt_nt = t_tc_tien_nt2 + t_thue_nt - t_ck_nt;

  return {
    t_so_luong,
    t_tien_nt2,
    t_tien2: t_tien_nt2,
    t_thue_nt,
    t_thue: t_thue_nt,
    t_km_nt,
    t_km: t_km_nt,
    t_ck_nt,
    t_ck: t_ck_nt,
    t_tc_tien_nt2,
    t_tc_tien2: t_tc_tien_nt2,
    t_tt_nt,
    t_tt: t_tt_nt
  };
};
