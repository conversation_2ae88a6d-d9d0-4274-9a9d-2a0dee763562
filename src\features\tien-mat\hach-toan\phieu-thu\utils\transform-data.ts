import { format } from 'date-fns';
import { transformDocumentNumber } from '@/components/custom/arito/form/document-number/util';
import { isValidUUID } from '@/lib/uuid-validator';
import { PhieuThuInput } from '@/types/schemas';
import { MA_CHUNG_TU } from '@/constants';
import { FormMode } from '@/types/form';

/**
 * Transform detail rows for PhieuThu API submission
 * @param detailRows - Array of detail row data from the form
 * @param dien_giai
 * @param formMode
 * @returns Transformed detail rows ready for API submission
 */
export const transformDetailRows = (detailRows: any[], dien_giai: string, formMode: FormMode) => {
  if (!detailRows || detailRows.length === 0) return [];

  return detailRows.map((row: any, index: number) => ({
    line: index + 1,
    ...(isValidUUID(row.uuid) && formMode !== 'add' && { uuid: row.uuid }),
    dien_giai: row.dien_giai || dien_giai,
    ma_kh: row.ma_kh_data?.uuid || row.ma_kh || '',
    id_hd: row.id_hd_data?.ID || row.id_hd || '',
    tk_co: row.id_hd_data?.tk_data?.uuid || row.tk_co || '',
    ty_gia_hd: row.ty_gia_hd?.toString() || '0',
    ty_gia2: row.ty_gia2,
    tien_nt: row.tien_nt || row.id_hd_data?.tien_con_phai_tt,
    tien: row.tien_nt,
    ma_bp: row.ma_bp_data?.uuid || row.ma_bp || '',
    ma_vv: row.ma_vv_data?.uuid || row.ma_vv || '',
    ma_hd: row.ma_hd_data?.uuid || row.ma_hd || '',
    ma_dtt: row.ma_dtt_data?.uuid || row.ma_dtt || '',
    ma_ku: row.ma_ku_data?.uuid || row.ma_ku || '',
    ma_phi: row.ma_phi_data?.uuid || row.ma_phi || '',
    ma_sp: row.ma_sp_data?.uuid || row.ma_sp || '',
    ma_lsx: row.ma_lsx || '',
    ma_cp0: row.ma_cp0_data?.uuid || row.ma_cp0 || ''
  }));
};

/**
 * Transform base form data for PhieuThu submission
 * @param data - Form data from the form submission
 * @param state - Form field state containing references to selected entities
 * @returns Transformed base data ready for API submission
 */
export const transformBaseData = (data: any, state: any): Partial<PhieuThuInput> => {
  return {
    ma_ngv: data.ma_ngv || '',
    dia_chi: data.dia_chi || '',
    ong_ba: data.ong_ba || '',
    dien_giai: data.dien_giai || '',

    tk: state.account?.uuid || data.tk || '',

    ...(() => {
      const docNumber = transformDocumentNumber(state.quyenChungTu, state.soChungTu, MA_CHUNG_TU.TIEN_MAT.PHIEU_THU);
      return {
        ...docNumber,
        i_so_ct: docNumber.i_so_ct?.toString() || ''
      };
    })(),

    ngay_ct: data.ngay_ct ? format(new Date(data.ngay_ct), 'yyyy-MM-dd') : '',
    ngay_lct: data.ngay_lct ? format(new Date(data.ngay_lct), 'yyyy-MM-dd') : '',
    ngay_ct0: data.ngay_ct0 ? format(new Date(data.ngay_ct0), 'yyyy-MM-dd') : '',

    ma_nt: data.ma_nt || 'VND',
    ty_gia: data.ty_gia?.toString() || '1',

    status: data.status || '0',
    so_ct0: data.so_ct0 || '',
    so_ct_goc: data.so_ct_goc?.toString() || '0',
    dien_giai_ct_goc: data.dien_giai_ct_goc || '',

    ma_tt: state.paymentTerm?.uuid || ''
  };
};

/**
 * Transform complete form data including detail rows for full submission
 * @param data - Form data from the form submission
 * @param state - Form field state containing references to selected entities
 * @param detailRows - Array of detail row data
 * @param calculatedData - Calculated totals from the form
 * @param entityUnit - Entity unit information
 * @param formMode
 * @returns Complete transformed data ready for API submission
 */
export const transformFormData = (
  data: any,
  state: any,
  detailRows: any[] = [],
  calculatedData: any,
  entityUnit: any,
  formMode: FormMode
): any => {
  const baseData = transformBaseData(data, state);
  const child_items = transformDetailRows(detailRows, data.dien_giai, formMode);

  return {
    ...baseData,
    unit_id: entityUnit?.uuid || '',

    t_tien_nt: calculatedData.t_tien_nt,
    t_tien: calculatedData.t_tien,
    ma_kh: child_items[0].ma_kh,

    child_items
  };
};

export const transformDataFromHoaDon = (hoaDonData?: any) => {
  const baseData = {
    ma_ngv: '1',
    dia_chi: hoaDonData?.dia_chi || '',
    ong_ba: hoaDonData?.ong_ba || '',
    dien_giai: `Thu tiền: ${hoaDonData?.dien_giai || ''}`,
    ma_nt: hoaDonData?.ma_nt,
    ty_gia: hoaDonData?.ty_gia,
    status: '5',
    transfer_yn: true,
    tk_data: null,
    ngay_ct0: new Date().toISOString().split('T')[0],
    so_ct: null
  };
  const chi_tiet = (hoaDonData?.chi_tiet || []).map((item: any) => ({
    dien_giai: `Thu tiền: ${hoaDonData?.dien_giai || ''}`,
    ma_kh_data: hoaDonData?.ma_kh_data,
    id_hd_data: {
      ID: hoaDonData?.uuid || '',
      da_thanh_toan: 0,
      ma_ct: hoaDonData?.ma_ct || '',
      ngay_ct: hoaDonData?.ngay_ct || '',
      so_ct: hoaDonData?.so_ct || '',
      tien_con_phai_tt: hoaDonData?.t_tt || 0,
      tien_hd_nt: hoaDonData?.t_tt_nt || 0,
      tien_tren_hd: hoaDonData?.t_tt_nt || 0,
      con_lai: hoaDonData?.t_tt || 0,
      ngoai_te: hoaDonData?.ma_nt_data?.ma_nt || '',
      tk_data: {
        uuid: hoaDonData?.tk_data?.uuid || '',
        tk: hoaDonData?.tk_data?.code || ''
      }
    },
    tien_nt: item.t_tt_nt,
    tien: item.t_tt,
    ma_bp_data: item.ma_bp_data,
    ma_vv_data: item.ma_vv_data,
    ma_hd_data: item.ma_hd_data,
    ma_dtt_data: item.ma_dtt_data,
    ma_ku_data: item.ma_ku_data,
    ma_phi_data: item.ma_phi_data,
    ma_sp_data: item.ma_sp_data,
    ma_cp0_data: null
  }));

  return {
    ...baseData,
    child_data: chi_tiet
  };
};
