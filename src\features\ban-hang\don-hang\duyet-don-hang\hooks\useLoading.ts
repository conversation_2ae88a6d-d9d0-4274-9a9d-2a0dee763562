import { useState, useEffect } from 'react';

interface UseLoadingOptions {
  duration?: number;
  isOpen?: boolean;
  external?: boolean;
}

/**
 * Loading hook that manages loading state for form dialogs
 *
 * @param options Configuration options
 * @param options.duration Duration in milliseconds before loading is set to false (default: 1500)
 * @param options.isOpen Whether the dialog/form is open
 * @param options.externalLoading External loading state (e.g., from API calls)
 * @returns Loading state object
 */
export function useLoading(options: UseLoadingOptions | number = {}) {
  // Handle backward compatibility - if a number is passed, treat it as duration
  const config =
    typeof options === 'number'
      ? { duration: options, isOpen: true, external: false }
      : { duration: 1500, isOpen: true, external: false, ...options };

  const { duration, isOpen, external } = config;
  const [internal, setInternal] = useState(false);

  useEffect(() => {
    if (!isOpen) {
      setInternal(false);
      return;
    }

    // Show loading when dialog opens
    setInternal(true);

    // Hide loading after specified duration
    const timer = setTimeout(() => {
      setInternal(false);
    }, duration);

    return () => clearTimeout(timer);
  }, [isOpen, duration]);

  return {
    loading: external || internal
  };
}

export default useLoading;
