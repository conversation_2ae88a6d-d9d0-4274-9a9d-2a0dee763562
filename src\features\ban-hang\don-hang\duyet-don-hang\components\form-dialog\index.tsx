'use client';

import { useState, useEffect, useMemo } from 'react';
import { ConfirmDialog, ApprovalDialog, RejectDialog, UndoConfirmDialog } from '../../components';
import { getFormTitle, getFormActionButtons, transformFormData } from '../../utils';
import { AritoHeaderTabs, AritoForm } from '@/components/custom/arito';
import { ApprovalHistoryTab } from './ApprovalHistoryTab';
import { ApprovalInfoTab } from './ApprovalInfoTab';
import { useAuth } from '@/contexts/auth-context';
import { initialFormValues } from '../../schema';
import { useFormFieldState } from '../../hooks';
import { BasicInfoTab } from './BasicInfoTab';
import { FormMode } from '@/types/form';
import { BottomBar } from './BottomBar';
import { DetailTab } from './DetailTab';
import { useDetailRows } from './hooks';
import { OtherTab } from './OtherTab';

interface FormDialogProps {
  formMode: FormMode;
  open: boolean;
  initialData?: any;
  onSubmit?: (data: any) => void;
  onClose: () => void;
  onAdd?: () => void;
  onEdit?: () => void;
  onDelete?: () => void;
  onCopy?: () => void;
  onSendForProcessing?: (cnote?: string) => Promise<boolean>;
  onApprove?: (action: 'approve' | 'skip', cnote: string) => Promise<boolean>;
  onReject?: (action: 'cancel' | 'reject', cnote: string) => Promise<boolean>;
  onUndo?: (cnote?: string) => Promise<boolean>;
}

const FormDialog = ({
  open,
  formMode,
  initialData,
  onClose,
  onSubmit,
  onAdd,
  onEdit,
  onDelete,
  onCopy,
  onSendForProcessing,
  onApprove,
  onReject,
  onUndo
}: FormDialogProps) => {
  const [isConfirm, setIsConfirm] = useState<boolean>(false);
  const [isFormDirty, setIsFormDirty] = useState<boolean>(false);
  const [activeTab, setActiveTab] = useState<string>('info');
  const [showApprovalDialog, setShowApprovalDialog] = useState<boolean>(false);
  const [showRejectDialog, setShowRejectDialog] = useState<boolean>(false);
  const [showUndoConfirmDialog, setShowUndoConfirmDialog] = useState<boolean>(false);
  const { entityUnit } = useAuth();
  const {
    rows: detailRows,
    selectedRowUuid: detailSelectedRowUuid,
    handleRowClick: detailHandleRowClick,
    handleAddRow: detailHandleAddRow,
    handleDeleteRow: detailHandleDeleteRow,
    handleCopyRow: detailHandleCopyRow,
    handlePasteRow: detailHandlePasteRow,
    handleMoveRow: detailHandleMoveRow,
    handleCellValueChange: detailHandleCellValueChange
  } = useDetailRows(initialData?.chi_tiet_data || []);
  const { state, actions } = useFormFieldState(initialData);

  useEffect(() => {
    if (!open) {
      setIsFormDirty(false);
      setActiveTab('info');
    }
  }, [open]);

  const handleSubmit = (data: any) => {
    const formData = transformFormData(data, state, detailRows, entityUnit);
    console.log('formData: ', formData);

    if (formMode === 'edit') {
      onSubmit?.({ ...formData, i_so_ct: initialData?.i_so_ct });
    } else {
      onSubmit?.(formData);
    }
    setIsFormDirty(false);
  };

  const handleClose = () => {
    if (isFormDirty && formMode !== 'view') {
      setIsConfirm(true);
    } else {
      onClose();
    }
  };

  const handleTabChange = (tabId: string) => {
    setActiveTab(tabId);
  };

  const handleSendForProcessing = async () => {
    const originalStatus = state.status;
    // Update status immediately for UI feedback
    actions.setStatus('1');
    // Pass the data to parent component for API call
    const success = await onSendForProcessing?.('Đơn hàng đã được kiểm tra và sẵn sàng xử lý');
    // If API failed, revert status
    if (!success) {
      actions.setStatus(originalStatus);
    }
  };

  const handleApprove = () => {
    // Show approval dialog instead of directly approving
    setShowApprovalDialog(true);
  };

  const handleApprovalConfirm = async (action: 'approve' | 'skip', comment?: string) => {
    const originalStatus = state.status;
    // Update status immediately for UI feedback
    actions.setStatus('5');
    // Pass the data to parent component for API call
    const success = await onApprove?.(action, comment || 'Đơn hàng đã được kiểm tra và phê duyệt');
    // If API failed, revert status
    if (!success) {
      actions.setStatus(originalStatus);
    }
    setShowApprovalDialog(false);
  };

  const handleApprovalClose = () => {
    setShowApprovalDialog(false);
  };

  const handleReject = () => {
    // Show reject dialog instead of directly rejecting
    setShowRejectDialog(true);
  };

  const handleRejectConfirm = async (action: 'cancel' | 'reject', reason?: string) => {
    const originalStatus = state.status;
    // Update status immediately for UI feedback
    actions.setStatus('9');
    // Pass the data to parent component for API call
    const success = await onReject?.(action, reason || 'Đơn hàng không đáp ứng yêu cầu');
    // If API failed, revert status
    if (!success) {
      actions.setStatus(originalStatus);
    }
    setShowRejectDialog(false);
  };

  const handleRejectClose = () => {
    setShowRejectDialog(false);
  };

  const handleUndo = () => {
    // Show undo confirm dialog
    setShowUndoConfirmDialog(true);
  };

  const handleUndoConfirm = async () => {
    const originalStatus = state.status;
    // Update status immediately for UI feedback
    actions.setStatus('4');
    // Pass the data to parent component for API call
    const success = await onUndo?.('Bỏ lệnh duyệt đơn hàng');
    // If API failed, revert status
    if (!success) {
      actions.setStatus(originalStatus);
    }
    setShowUndoConfirmDialog(false);
  };

  const handleUndoClose = () => {
    setShowUndoConfirmDialog(false);
  };

  const title = getFormTitle(formMode, activeTab);
  const actionButtons = getFormActionButtons(
    formMode,
    activeTab,
    {
      onAdd,
      onEdit,
      onDelete,
      onCopy,
      handleClose,
      onSendForProcessing: handleSendForProcessing,
      onApprove: handleApprove,
      onReject: handleReject,
      onUndo: handleUndo
    },
    state.status
  );

  return (
    <>
      <AritoForm
        mode={formMode}
        initialData={initialData || initialFormValues}
        title={title}
        actionButtons={actionButtons}
        subTitle='Đơn hàng từ bán hàng'
        onSubmit={handleSubmit}
        onClose={handleClose}
        headerFields={
          <div onChange={() => setIsFormDirty(true)}>
            <AritoHeaderTabs
              tabs={[
                {
                  id: 'info',
                  label: 'Thông tin',
                  component: <BasicInfoTab formMode={formMode} formState={{ state, actions }} />
                }
              ]}
              onTabChange={handleTabChange}
              defaultTabIndex={activeTab === 'info' ? 0 : activeTab === 'history' ? 1 : 0}
            />
          </div>
        }
        tabs={
          activeTab === 'info' && [
            {
              id: 'details',
              label: 'Chi tiết',
              component: (
                <DetailTab
                  formMode={formMode}
                  rows={detailRows}
                  selectedRowUuid={detailSelectedRowUuid}
                  onRowClick={detailHandleRowClick}
                  onAddRow={detailHandleAddRow}
                  onDeleteRow={detailHandleDeleteRow}
                  onCopyRow={detailHandleCopyRow}
                  onPasteRow={detailHandlePasteRow}
                  onMoveRow={detailHandleMoveRow}
                  onCellValueChange={detailHandleCellValueChange}
                />
              )
            },
            {
              id: 'other',
              label: 'Khác',
              component: <OtherTab formMode={formMode} formState={{ state, actions }} />
            },
            {
              id: 'approval-info',
              label: 'Thông tin duyệt (2/2)',
              component: <ApprovalInfoTab formMode={formMode} formState={{ state, actions }} />
            },
            {
              id: 'approval-history',
              label: 'Lịch sử duyệt/hủy',
              component: (
                <ApprovalHistoryTab formMode={formMode} documentId={initialData?.uuid} formState={{ state, actions }} />
              )
            }
          ]
        }
        bottomBar={activeTab === 'info' && <BottomBar detailRows={detailRows} state={state} />}
      />

      {isConfirm && (
        <ConfirmDialog
          open={isConfirm}
          onClose={() => setIsConfirm(false)}
          onConfirm={() => {
            setIsConfirm(false);
            setIsFormDirty(false);
            onClose();
          }}
          title='Cảnh báo'
          content='Bạn muốn kết thúc?'
        />
      )}

      {showApprovalDialog && (
        <ApprovalDialog
          open={showApprovalDialog}
          onClose={handleApprovalClose}
          onConfirm={handleApprovalConfirm}
          title='Phê duyệt và thực hiện bỏ qua bước'
        />
      )}

      {showRejectDialog && (
        <RejectDialog
          open={showRejectDialog}
          onClose={handleRejectClose}
          onConfirm={handleRejectConfirm}
          title='Hủy/từ chối duyệt đơn từ'
        />
      )}

      {showUndoConfirmDialog && (
        <UndoConfirmDialog
          open={showUndoConfirmDialog}
          onClose={handleUndoClose}
          onConfirm={handleUndoConfirm}
          title='Hỏi'
          content='Bạn có chắc chắn sẽ bỏ lệnh (undo) các đơn từ được chọn không?'
        />
      )}
    </>
  );
};

export default FormDialog;
