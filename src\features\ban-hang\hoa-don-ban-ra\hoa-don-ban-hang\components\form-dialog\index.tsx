'use client';

import { useState, useEffect, useMemo } from 'react';
import { getFormTitle, getFormActionButtons, calculateTotals, transformFormData } from '../../utils';
import { AritoHeaderTabs, AritoForm, LoadingOverlay } from '@/components/custom/arito';
import { formSchema, initialFormValues } from '../../schema';
import { useFormFieldState, useLoading } from '../../hooks';
import { useDetailRows, useAccountRows } from './hooks';
import { useAuth } from '@/contexts/auth-context';
import { AccountInfoTab } from './AccountInfoTab';
import { ConfirmDialog } from '../../components';
import { HoverDropdown } from './HoverDropdown';
import { BasicInfoTab } from './BasicInfoTab';
import InfoDropdown from './InfoDropdown';
import { HistoryTab } from './HistoryTab';
import { FormMode } from '@/types/form';
import { BottomBar } from './BottomBar';
import { DetailTab } from './DetailTab';
import { OtherTab } from './OtherTab';
import { HDDTTab } from './HDDTTab';

interface FormDialogProps {
  formMode: FormMode;
  open: boolean;
  initialData?: any;
  isCopyMode?: boolean;
  onSubmit?: (data: any) => void;
  onClose: () => void;
  onAdd?: () => void;
  onEdit?: () => void;
  onDelete?: () => void;
  onCopy?: () => void;
}

const FormDialog = ({
  open,
  formMode,
  initialData,
  isCopyMode = false,
  onClose,
  onSubmit,
  onAdd,
  onEdit,
  onDelete,
  onCopy
}: FormDialogProps) => {
  const [isConfirm, setIsConfirm] = useState<boolean>(false);
  const [isFormDirty, setIsFormDirty] = useState<boolean>(false);
  const [activeTab, setActiveTab] = useState<string>('info');
  const { entityUnit } = useAuth();

  // Process initial data for copy mode - remove UUIDs from detail rows
  const processInitialDataForCopy = (data: any) => {
    if (!data || !isCopyMode) return data;

    const processedData = { ...data };

    // Remove UUIDs from chi_tiet (detail rows)
    if (processedData.chi_tiet && Array.isArray(processedData.chi_tiet)) {
      processedData.chi_tiet = processedData.chi_tiet.map((item: any) => {
        const { uuid, ...itemWithoutUuid } = item;
        return {
          ...itemWithoutUuid,
          uuid: `temp-${Date.now()}-${Math.random()}` // Generate temporary UUID for frontend use
        };
      });
    }

    // Remove UUIDs from thong_tin_thanh_toan (account rows)
    if (processedData.thong_tin_thanh_toan && Array.isArray(processedData.thong_tin_thanh_toan)) {
      processedData.thong_tin_thanh_toan = processedData.thong_tin_thanh_toan.map((item: any) => {
        const { uuid, ...itemWithoutUuid } = item;
        return {
          ...itemWithoutUuid,
          uuid: `temp-${Date.now()}-${Math.random()}` // Generate temporary UUID for frontend use
        };
      });
    }

    return processedData;
  };

  const processedInitialData = processInitialDataForCopy(initialData);
  const {
    rows: detailRows,
    selectedRowUuid: detailSelectedRowUuid,
    handleRowClick: detailHandleRowClick,
    handleAddRow: detailHandleAddRow,
    handleDeleteRow: detailHandleDeleteRow,
    handleCopyRow: detailHandleCopyRow,
    handlePasteRow: detailHandlePasteRow,
    handleMoveRow: detailHandleMoveRow,
    handleCellValueChange: detailHandleCellValueChange
  } = useDetailRows(processedInitialData?.chi_tiet || []);
  const {
    rows: accountRows,
    selectedRowUuid: accountSelectedRowUuid,
    handleRowClick: accountHandleRowClick,
    handleAddRow: accountHandleAddRow,
    handleDeleteRow: accountHandleDeleteRow,
    handleCopyRow: accountHandleCopyRow,
    handlePasteRow: accountHandlePasteRow,
    handleMoveRow: accountHandleMoveRow,
    handleCellValueChange: accountHandleCellValueChange
  } = useAccountRows(processedInitialData?.thong_tin_thanh_toan || []);
  const { state, actions } = useFormFieldState(processedInitialData);

  useEffect(() => {
    if (!open) {
      setIsFormDirty(false);
      setActiveTab('info');
    }
  }, [open]);

  const { tong_sl, tong_tien, tong_thue, tong_tien_von, tong_thanh_toan, tong_ck } = useMemo(() => {
    return calculateTotals(detailRows);
  }, [detailRows]);

  const handleSubmit = (data: any) => {
    const totals = { tong_sl, tong_tien, tong_thue, tong_tien_von, tong_thanh_toan, tong_ck };
    const formData = transformFormData(data, state, detailRows, accountRows, totals);
    console.log('formData: ', { ...formData, unit_id: entityUnit?.uuid });

    if (formMode === 'edit') {
      onSubmit?.({ ...formData, unit_id: entityUnit?.uuid, i_so_ct: initialData?.i_so_ct });
    } else {
      onSubmit?.({ ...formData, unit_id: entityUnit?.uuid });
    }
    setIsFormDirty(false);
  };

  const handleClose = () => {
    if (isFormDirty && formMode !== 'view') {
      setIsConfirm(true);
    } else {
      onClose();
    }
  };

  const handleTabChange = (tabId: string) => {
    setActiveTab(tabId);
  };

  const title = getFormTitle(formMode, activeTab);
  const actionButtons = getFormActionButtons(formMode, activeTab, {
    onAdd,
    onEdit,
    onDelete,
    onCopy,
    handleClose
  });

  return (
    <>
      <AritoForm
        mode={formMode}
        initialData={processedInitialData || initialFormValues}
        title={title}
        schema={formSchema}
        actionButtons={actionButtons}
        subTitle='Hóa đơn bán hàng'
        onSubmit={handleSubmit}
        onClose={handleClose}
        headerFields={
          <div onChange={() => setIsFormDirty(true)}>
            <AritoHeaderTabs
              tabs={[
                {
                  id: 'info',
                  label: 'Thông tin',
                  component: <BasicInfoTab formMode={formMode} formState={{ state, actions }} />
                },
                ...(formMode === 'view'
                  ? [
                      {
                        id: 'history',
                        label: 'Lịch sử',
                        component: <HistoryTab />
                      }
                    ]
                  : [])
              ]}
              onTabChange={handleTabChange}
              defaultTabIndex={activeTab === 'info' ? 0 : activeTab === 'history' ? 1 : 0}
              actionButtons={formMode === 'view' && <InfoDropdown uuid={initialData?.uuid} />}
            />
          </div>
        }
        tabs={
          activeTab === 'info' && [
            {
              id: 'details',
              label: 'Chi tiết',
              component: (
                <DetailTab
                  formMode={formMode}
                  rows={detailRows}
                  selectedRowUuid={detailSelectedRowUuid}
                  onRowClick={detailHandleRowClick}
                  onAddRow={detailHandleAddRow}
                  onDeleteRow={detailHandleDeleteRow}
                  onCopyRow={detailHandleCopyRow}
                  onPasteRow={detailHandlePasteRow}
                  onMoveRow={detailHandleMoveRow}
                  onCellValueChange={detailHandleCellValueChange}
                />
              )
            },
            {
              id: 'hddt',
              label: 'HĐĐT(Không sử dụng)',
              component: <HDDTTab formMode={formMode} />
            },
            {
              id: 'other',
              label: 'Khác',
              component: <OtherTab formMode={formMode} formState={{ state, actions }} />
            },
            ...(state.pt_tao_yn && state.ma_httt === 'KB'
              ? [
                  {
                    id: 'account',
                    label: 'Thông tin tài khoản',
                    component: (
                      <AccountInfoTab
                        formMode={formMode}
                        rows={accountRows}
                        selectedRowUuid={accountSelectedRowUuid}
                        onRowClick={accountHandleRowClick}
                        onAddRow={accountHandleAddRow}
                        onDeleteRow={accountHandleDeleteRow}
                        onCopyRow={accountHandleCopyRow}
                        onPasteRow={accountHandlePasteRow}
                        onMoveRow={accountHandleMoveRow}
                        onCellValueChange={accountHandleCellValueChange}
                      />
                    )
                  }
                ]
              : [])
          ]
        }
        bottomBar={
          activeTab === 'info' && (
            <BottomBar
              tong_sl={tong_sl}
              tong_tien={tong_tien}
              tong_thue={tong_thue}
              tong_tien_von={tong_tien_von}
              tong_thanh_toan={tong_thanh_toan}
              tong_ck={tong_ck}
              state={state}
            />
          )
        }
      />

      {isConfirm && (
        <ConfirmDialog
          open={isConfirm}
          onClose={() => setIsConfirm(false)}
          onConfirm={() => {
            setIsConfirm(false);
            setIsFormDirty(false);
            onClose();
          }}
          title='Cảnh báo'
          content='Bạn muốn kết thúc?'
        />
      )}
    </>
  );
};

export default FormDialog;
