import React, { useState } from 'react';
import { Button } from '@mui/material';
import { AritoDialog, AritoIcon } from '@/components/custom/arito';
import { Label } from '@/components/ui/label';

interface ApprovalDialogProps {
  open: boolean;
  onClose: () => void;
  onConfirm: (action: 'approve' | 'skip', comment?: string) => void;
  title?: string;
}

const ApprovalDialog = ({
  open,
  onClose,
  onConfirm,
  title = 'Phê duyệt và thực hiện bỏ qua bước'
}: ApprovalDialogProps) => {
  const [selectedAction, setSelectedAction] = useState<'approve' | 'skip'>('approve');
  const [comment, setComment] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);

  const handleConfirm = () => {
    onConfirm(selectedAction, comment);
    onClose();
  };

  const handleClose = () => {
    // Reset form when closing
    setSelectedAction('approve');
    setComment('');
    onClose();
  };

  return (
    <AritoDialog
      open={open}
      onClose={handleClose}
      title={title}
      maxWidth='lg'
      disableBackdropClose={false}
      disableEscapeKeyDown={true}
      showFullscreenToggle={false}
      titleIcon={<AritoIcon icon={260} />}
      actions={
        <>
          <Button
            onClick={handleConfirm}
            type='submit'
            variant='contained'
            className='bg-[rgba(15,118,110,0.9)] normal-case hover:bg-[rgba(15,118,110,1)]'
          >
            <AritoIcon icon={884} className='mr-2' />
            Đồng ý
          </Button>
          <Button onClick={handleClose} variant='outlined'>
            <AritoIcon icon={885} className='mr-2' />
            Hủy
          </Button>
        </>
      }
    >
      <div className='min-w-[700px] p-4'>
        {/* Xử lý section */}
        <div className='mb-4'>
          <div className='flex items-center gap-6'>
            <Label className='min-w-[60px] text-sm font-medium'>Xử lý</Label>
            <div className='flex items-center gap-6'>
              <label className='flex items-center'>
                <input
                  type='radio'
                  name='action'
                  value='approve'
                  checked={selectedAction === 'approve'}
                  onChange={e => setSelectedAction(e.target.value as 'approve')}
                  className='mr-2'
                />
                <span className='text-sm'>Phê duyệt</span>
              </label>
              <label className='flex items-center'>
                <input
                  type='radio'
                  name='action'
                  value='skip'
                  checked={selectedAction === 'skip'}
                  onChange={e => setSelectedAction(e.target.value as 'skip')}
                  className='mr-2'
                />
                <span className='text-sm'>Bỏ qua (không liên quan đến luồng quy trình)</span>
              </label>
            </div>
          </div>
        </div>

        {/*Lời nhắn section */}
        <div className='mb-4'>
          <div className='flex items-start'>
            <Label className='mt-2 min-w-[60px] text-sm font-medium'>Lời nhắn</Label>
            <div className='ml-6 flex-1'>
              <textarea
                value={comment}
                onChange={e => setComment(e.target.value)}
                placeholder='Lời nhắn thông tin đến các user nhận của bước duyệt tiếp theo'
                className='min-h-[80px] w-full resize-none rounded border border-gray-300 p-2 text-sm focus:border-blue-500 focus:outline-none'
                rows={3}
              />
            </div>
          </div>
        </div>
      </div>
    </AritoDialog>
  );
};

export default ApprovalDialog;
