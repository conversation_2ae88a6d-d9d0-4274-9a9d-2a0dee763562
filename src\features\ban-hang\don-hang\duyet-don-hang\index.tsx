'use client';

import { useState } from 'react';
import {
  SearchDialog,
  ActionBar,
  FormDialog,
  ReviewProgressDialog,
  ReviewConfigDialog,
  EditApprovalDialog
} from './components';
import { AritoDataTables, DeleteDialog, LoadingOverlay, AritoColoredDot } from '@/components/custom/arito';
import { useLoading, useApproval, useDialogState, useDuyetDonHang } from './hooks';
import { useFormState, useRows, useDonHang } from '@/hooks';
import { getDataTableColumns } from './cols-definition';

export default function DuyetDonHangPage() {
  const [showFilterTree, setShowFilterTree] = useState(false);
  const [showSearchDialog, setShowSearchDialog] = useState(false);
  const { donHangs, isLoading, approveDonHang, addDonHang, updateDonHang, deleteDonHang, refreshDonHangs } =
    useDonHang();
  const { selectedObj, selectedRowIndex, handleRowClick, clearSelection } = useRows();
  const {
    showForm,
    showDelete,
    formMode,
    isCopyMode,

    handleCloseForm,
    handleCloseDelete,
    handleAddClick,
    handleEditClick,
    handleViewClick,
    handleDeleteClick,
    handleCopyClick
  } = useFormState();
  const { loading } = useLoading({
    isOpen: showForm,
    external: false,
    duration: 500
  });
  // Keep old useApproval for backward compatibility if needed
  // const { approveDocument, rejectDocument, sendForProcessing, undoDocument } = useApproval();
  const {
    submitForProcessing,
    approveDocument: apiApproveDocument,
    rejectDocument: apiRejectDocument,
    unapproveDocument,
    updateApprovalLevel,
    isLoading: isApiLoading,
    error: apiError
  } = useDuyetDonHang();
  const {
    reviewProgressDialogOpen,
    handleOpenReviewProgressDialog,
    handleCloseReviewProgressDialog,
    reviewConfigDialogOpen,
    handleOpenReviewConfigDialog,
    handleCloseReviewConfigDialog
  } = useDialogState();

  // State for EditApprovalDialog
  const [editApprovalDialogOpen, setEditApprovalDialogOpen] = useState(false);

  const handleSearch = () => {
    setShowSearchDialog(true);
  };

  const handleSearchClose = () => {
    setShowSearchDialog(false);
  };

  const handleSearchSubmit = (filters: any) => {
    console.log('Search filters:', filters);
    // TODO: Implement search functionality here
  };

  const handleSubmit = async (data: any) => {
    try {
      if (formMode === 'add') {
        await addDonHang(data);
      } else if (formMode === 'edit' && selectedObj) {
        await updateDonHang(selectedObj.uuid, data);
      }
      handleCloseForm();
      clearSelection();
    } catch (error) {
      console.error('Error submitting form:', error);
    }
  };

  // Wrapper functions for FormDialog callbacks
  const handleSendForProcessing = async (cnote?: string): Promise<boolean> => {
    if (selectedObj?.uuid) {
      const success = await submitForProcessing(selectedObj.uuid, cnote);
      if (success) {
        console.log('Document sent for processing successfully');
        // Update the selected object status for immediate UI feedback
        if (selectedObj) {
          selectedObj.status = '1'; // Update to "Chờ duyệt"
        }
        refreshDonHangs();
      }
      return success;
    }
    return false;
  };

  const handleApprove = async (action: 'approve' | 'skip', cnote: string): Promise<boolean> => {
    if (selectedObj?.uuid) {
      const success = await apiApproveDocument(selectedObj.uuid, cnote, action);
      if (success) {
        console.log('Document approved successfully');
        // Update the selected object status for immediate UI feedback
        if (selectedObj) {
          selectedObj.status = '5'; // Update to "Đã duyệt"
        }
        refreshDonHangs();
      }
      return success;
    }
    return false;
  };

  const handleReject = async (action: 'cancel' | 'reject', cnote: string): Promise<boolean> => {
    if (selectedObj?.uuid) {
      const success = await apiRejectDocument(selectedObj.uuid, cnote, action);
      if (success) {
        console.log('Document rejected successfully');
        // Update the selected object status for immediate UI feedback
        if (selectedObj) {
          selectedObj.status = '9'; // Update to "Đóng"
        }
        refreshDonHangs();
      }
      return success;
    }
    return false;
  };

  const handleUndo = async (cnote?: string): Promise<boolean> => {
    if (selectedObj?.uuid) {
      const success = await unapproveDocument(selectedObj.uuid, cnote);
      if (success) {
        console.log('Document undone successfully');
        // Update the selected object status for immediate UI feedback
        if (selectedObj) {
          selectedObj.status = '4'; // Update to "Đang duyệt"
        }
        refreshDonHangs();
      }
      return success;
    }
    return false;
  };

  const handleApproveClick = () => {
    approveDonHang(selectedObj.uuid);
  };

  // EditApprovalDialog handlers
  const handleOpenEditApprovalDialog = () => {
    if (selectedObj) {
      setEditApprovalDialogOpen(true);
    }
  };

  const handleCloseEditApprovalDialog = () => {
    setEditApprovalDialogOpen(false);
  };

  const handleEditApprovalSubmit = async (approvalData: any) => {
    console.log('Edit approval submitted:', approvalData);

    if (selectedObj?.uuid) {
      try {
        // approvalData đã được format đúng từ EditApprovalDialog
        // { approve: [...], followers: [...] }

        const success = await updateApprovalLevel(selectedObj.uuid, approvalData);

        if (success) {
          console.log('Approval level updated successfully');
          refreshDonHangs();
        } else {
          console.error('Failed to update approval level');
        }
      } catch (error) {
        console.error('Error updating approval level:', error);
      }
    }
  };

  const tables = [
    {
      name: 'Cần xử lý',
      rows: donHangs.filter(row => row.status === '1'),
      columns: getDataTableColumns(handleViewClick, '1'),
      icon: <AritoColoredDot color='red' className='mr-2' />
    },
    {
      name: 'Đã xử lý',
      rows: donHangs.filter(row => row.status === '5' || row.status === '9'),
      columns: getDataTableColumns(handleViewClick, '5'),
      icon: <AritoColoredDot color='#F44336' className='mr-2' />
    },
    {
      name: 'Đang theo dõi',
      rows: donHangs.filter(row => row.status === '-4'),
      columns: getDataTableColumns(handleViewClick, '-4'),
      icon: <AritoColoredDot color='#4CAF50' className='mr-2' />
    },
    {
      name: 'Tất cả',
      rows: donHangs,
      columns: getDataTableColumns(handleViewClick, ''),
      icon: <AritoColoredDot color='#212121' className='mr-2' />
    },
    {
      name: 'Nháp',
      rows: donHangs.filter(row => row.status === '0'),
      columns: getDataTableColumns(handleViewClick, '0'),
      icon: <AritoColoredDot color='#FCE4EC' className='mr-2' />
    },
    {
      name: 'Đang xử lý',
      rows: donHangs.filter(row => row.status === '1'),
      columns: getDataTableColumns(handleViewClick, '1'),
      icon: <AritoColoredDot color='#2196F3' className='mr-2' />
    },
    {
      name: 'Hoàn thành',
      rows: donHangs.filter(row => row.status === '5'),
      columns: getDataTableColumns(handleViewClick, '5'),
      icon: <AritoColoredDot color='#64B5F6' className='mr-2' />
    },
    {
      name: 'Đóng',
      rows: donHangs.filter(row => row.status === '9'),
      columns: getDataTableColumns(handleViewClick, '9'),
      icon: <AritoColoredDot color='#9E9E9E' className='mr-2' />
    }
  ];

  return (
    <div className='flex h-full min-h-[calc(100vh-120px)] w-full flex-col overflow-hidden'>
      {showSearchDialog && (
        <SearchDialog open={showSearchDialog} onClose={handleSearchClose} onSearch={handleSearchSubmit} />
      )}

      {reviewProgressDialogOpen && (
        <ReviewProgressDialog
          isOpen={reviewProgressDialogOpen}
          onClose={handleCloseReviewProgressDialog}
          onSubmit={values => {
            console.log('Submitted values:', values);
            handleCloseReviewProgressDialog();
          }}
        />
      )}
      {reviewConfigDialogOpen && (
        <ReviewConfigDialog
          isOpen={reviewConfigDialogOpen}
          onClose={handleCloseReviewConfigDialog}
          onSubmit={values => {
            console.log('Submitted values:', values);
            handleCloseReviewConfigDialog();
          }}
        />
      )}

      {editApprovalDialogOpen && (
        <EditApprovalDialog
          open={editApprovalDialogOpen}
          onClose={handleCloseEditApprovalDialog}
          onSubmit={handleEditApprovalSubmit}
          initialData={selectedObj}
          formMode='edit'
        />
      )}

      {loading && (
        <div className='absolute inset-0 z-50 flex items-center justify-center bg-white bg-opacity-80'>
          <LoadingOverlay />
        </div>
      )}

      {showForm && (
        <FormDialog
          formMode={formMode}
          open={showForm}
          onClose={handleCloseForm}
          initialData={
            selectedObj && (formMode === 'edit' || formMode === 'view' || (formMode === 'add' && isCopyMode))
              ? selectedObj
              : formMode === 'add' && !isCopyMode
                ? undefined
                : undefined
          }
          onSubmit={handleSubmit}
          onAdd={handleAddClick}
          onEdit={handleEditClick}
          onDelete={handleDeleteClick}
          onCopy={handleCopyClick}
          onSendForProcessing={handleSendForProcessing}
          onApprove={handleApprove}
          onReject={handleReject}
          onUndo={handleUndo}
        />
      )}

      {showDelete && (
        <DeleteDialog
          open={showDelete}
          onClose={handleCloseDelete}
          selectedObj={selectedObj}
          deleteObj={deleteDonHang}
          clearSelection={clearSelection}
        />
      )}

      {!showForm && !editApprovalDialogOpen && (
        <>
          <ActionBar
            onEditClick={handleOpenEditApprovalDialog}
            onRefreshClick={refreshDonHangs}
            onFixedColumnsClick={() => console.log('Fix column clicked')}
            onExportDataClick={() => console.log('Export data clicked')}
            onApproveClick={handleApproveClick}
            onReviewProgressClick={handleOpenReviewProgressDialog}
            onReviewConfigClick={handleOpenReviewConfigDialog}
            onHideShowFilterClick={() => setShowFilterTree(prev => !prev)}
          />

          {isLoading && (
            <div className='flex h-full items-center justify-center'>
              <LoadingOverlay />
            </div>
          )}

          {!isLoading && (
            <div className='flex-1 overflow-hidden'>
              <AritoDataTables
                tables={tables}
                onRowClick={handleRowClick}
                selectedRowId={selectedRowIndex || undefined}
              />
            </div>
          )}
        </>
      )}
    </div>
  );
}
