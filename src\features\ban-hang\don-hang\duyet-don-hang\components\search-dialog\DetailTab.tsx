import React from 'react';
import {
  loSearchColumns,
  QUERY_KEYS,
  vatTuSearchColumns,
  viTriSearchColumns,
  warehouseSearchColumns
} from '@/constants';
import { KhoHang, Lo, VatTu, ViTri } from '@/types/schemas';
import { SearchField } from '@/components/custom/arito';
import { Label } from '@/components/ui/label';
import { FormMode } from '@/types/form';

interface DetailTabProps {
  formMode: FormMode;
}

const DetailTab = ({ formMode }: DetailTabProps) => {
  return (
    <div className='p-4'>
      {/* Mã vật tư */}
      <div className='flex items-center'>
        <Label className='w-[150px]'>Mã vật tư</Label>
        <SearchField<VatTu>
          type='text'
          name='ma_vt'
          searchEndpoint={`/${QUERY_KEYS.VAT_TU}/`}
          searchColumns={vatTuSearchColumns}
          columnDisplay='ma_vt'
          displayRelatedField='ten_vt'
          dialogTitle='Danh mục vật tư'
          className='w-[205px]'
        />
      </div>

      {/* Mã kho */}
      <div className='flex items-center'>
        <Label className='w-[150px]'>Mã kho</Label>
        <SearchField<KhoHang>
          type='text'
          name='ma_kho'
          searchEndpoint={`/${QUERY_KEYS.KHO_HANG}/`}
          searchColumns={warehouseSearchColumns}
          columnDisplay='ma_kho'
          displayRelatedField='ten_kho'
          dialogTitle='Danh mục kho hàng'
          className='w-[205px]'
        />
      </div>
    </div>
  );
};

export default DetailTab;
